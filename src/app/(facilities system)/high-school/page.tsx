"use client";

import { BsArrowDownShort } from "react-icons/bs";

import Image from "next/image";
import Link from "next/link";

export default function HighSchoolHomePage() {
  // data.js
  const articles = [
    {
      id: 2,
      category:
        "Trường tiểu học- THCS Thăng Long (Trường ĐH Thủ đô Hà Nội) có thêm cấp THPT",
      title:
        "<PERSON><PERSON>y 4/9, Trường ĐH Thủ đô Hà Nội tổ chức lễ công bố quyết định tổ chức lại Trường Tiểu học và THCS Thăng Long thành Trường Tiểu học, THCS và THPT Thăng Long.",
      imageUrl:
        "https://static.kinhtedothi.vn/w960/images/upload/2024/09/04/a-thu-do-1.jpg",
      link: "/high-school/news/2"
    },
    {
      id: 1,
      category:
        "Teen THPT Trần Phú - <PERSON><PERSON><PERSON> sáng tạo sách mô hình 3D Một vòng Hoàn <PERSON>",
      title:
        "<PERSON>ào mừng lễ Quốc khánh 2/9 và khai giảng năm học mới, Chi đoàn 12A6 trường THPT Trần Phú - Hoàn Kiếm đã gửi gắm những giá trị về lịch sử văn hóa dân tộc",
      imageUrl:
        "https://image.tienphong.vn/w1966/Uploaded/2024/ttf-ztmfxuzt/2024_08_28/bia-4809.jpg",
      link: "/high-school/news/1"
    },
    {
      id: 3,
      category: "Trường THPT Kim Liên – 48 năm sen vàng tỏa sáng",
      title:
        "Trường THPT Kim Liên được thành lập từ năm 1974. Trải qua 48 năm xây dựng và trưởng thành nhà trường đã tạo dựng được uy tín vững chắc, trở thành một thương hiệu giáo dục tin cậy",
      imageUrl:
        "data:image/jpeg;base64,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",
      link: "/high-school/news/3"
    },
    {
      id: 4,
      category:
        "Thầy trò Trường THPT Việt Đức tự hào truyền thống, sải cánh vươn xa",
      title:
        "Sáng nay (5/9), toàn thể cán bộ, giáo viên và hơn 2.550 học sinh Trường THPT Việt Đức long trọng tổ chức lễ khai giảng, hân hoan bước vào năm học mới 2024 - 2025",
      imageUrl:
        "https://cdn.tuoitrethudo.vn/stores/news_dataimages/2024/092024/05/11/2235-1f6fb37822b585ebdca420240905110050.7276050.jpg",
      link: "/high-school/news/4"
    }
  ];
  const dataCart = [
    {
      id: 1,
      title: "Chương trình học linh hoạt",
      description:
        "Chương trình học được thiết kế để vừa đảm bảo kiến thức cơ bản, vừa khuyến khích sự sáng tạo, giúp học sinh có cơ hội khám phá và phát triển sở thích cá nhân.",
      imageUrl: "/img/news/img7.png",
      link: "/high-school"
    },
    {
      id: 2,
      title: "Giáo viên sáng tạo",
      description:
        "Giáo viên không chỉ là người truyền đạt kiến thức mà còn là người hướng dẫn, khuyến khích học sinh đặt câu hỏi và tìm kiếm câu trả lời một cách độc lập.",
      imageUrl: "/img/news/img8.png",
      link: "/high-school/creative-teacher"
    },
    {
      id: 3,
      title: "Sử dụng công nghệ",
      description:
        "Các công nghệ thông tin và truyền thông (CNTT-TT) được ứng dụng vào việc giảng dạy và học tập, giúp học sinh tiếp cận thông tin dễ dàng và nâng cao kỹ năng số.",
      imageUrl: "/img/news/img6.png",
      link: "/high-school"
    },
    {
      id: 4,
      title: "Môi trường học tập năng động",
      description:
        "Không gian lớp học linh hoạt, có thể thay đổi để phù hợp với các hoạt động học tập khác nhau, khuyến khích sự tương tác và hợp tác giữa các học sinh.",
      imageUrl: "/img/news/img10.png",
      link: "/high-school"
    },
    {
      id: 5,
      title: "Dự án và hoạt động thực tế",
      description:
        "Học sinh tham gia vào các dự án thực tế, cuộc thi, hoặc các hoạt động ngoại khóa, giúp họ áp dụng kiến thức vào thực tiễn và phát triển kỹ năng mềm.",
      imageUrl: "/img/news/img14.svg",
      link: "/high-school"
    },
    {
      id: 6,
      title: "Đánh giá toàn diện",
      description:
        "Hệ thống đánh giá không chỉ dựa vào điểm số, mà còn xem xét quá trình học tập, sự sáng tạo và khả năng cộng tác của học sinh.",
      imageUrl: "/img/news/img13.png",
      link: "/high-school"
    }
  ];
  const dataCart2 = [
    {
      id: 1,
      title: "Học tập dựa trên dự án (Project-Based Learning - PBL)",
      imageUrl: "/high-school/mohinh1.jpg"
    },
    {
      id: 2,
      title: "Học tập trải nghiệm (Experiential Learning)",
      imageUrl: "/high-school/mohinh2.jpg"
    },
    {
      id: 3,
      title: "Học tập cá thể hóa (Personalized Learning)",
      imageUrl: "/high-school/mohinh3.jpg"
    },
    {
      id: 4,
      title: "Học kết hợp (Blended Learning)",
      imageUrl: "/high-school/mohinh4.jpg"
    },
    {
      id: 52,
      title: "Học tập dựa vào nghiên cứu (Inquiry-Based Learning)",
      imageUrl: "/high-school/mohinh5.jpg"
    },
    {
      id: 53,
      title: "Mô hình Flipped Classroom (Lớp học đảo ngược)",
      imageUrl: "/high-school/mohinh6.jpg"
    },
    {
      id: 54,
      title:
        "Giáo dục STEAM (Science, Technology, Engineering, Arts, Mathematics)",
      imageUrl: "/high-school/mohinh7.jpg"
    },
    {
      id: 541,
      title: "Mô hình giáo dục toàn diện (Holistic Education)",
      imageUrl: "/high-school/mohinh8.jpg"
    }
  ];
  const dataCart3 = [
    {
      id: 1,
      title: "Tìm hiểu bản thân",
      imageUrl: "/icon/icon-thpt1.png"
    },
    {
      id: 2,
      title: "Khám phá nghề nghiệp",
      imageUrl: "/icon/icon-thpt2.png"
    },
    {
      id: 4,
      title: "Đặt mục tiêu nghề nghiệp",
      imageUrl: "/icon/icon-thpt3.png"
    },
    {
      id: 5,
      title: "Định hướng học tập",
      imageUrl: "/icon/icon-thpt4.png"
    },
    {
      id: 5,
      title: "Phát triển kỹ năng mềm",
      imageUrl: "/icon/icon-thpt5.png"
    },
    {
      id: 66,
      title: "Tìm kiếm sự hỗ trợ",
      imageUrl: "/icon/icon-thpt6.png"
    },
    {
      id: 55,
      title: "Liên tục đánh giá và<br/> điều chỉnh",
      imageUrl: "/icon/icon-thpt7.png"
    }
  ];
  const articleList = [
    {
      id: 1,
      title: "Trường TH, THCS, THPT Thăng Long",
      publisher:
        "Trường TH,THCS&THPT Thăng Long, Cơ sở 4- Trường Đại học Thủ đô Hà Nội, Thị trấn Thường Tín, Huyện Thường Tín, TP Hà Nội.",
      publisher1: null,
      year: "Ngày tham gia 21/01/2024",
      imageUrl:
        "data:image/jpeg;base64,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",
      href: "https://hnmu.edu.vn/gioi-thieu-truong-lien-cap-thang-long"
    },
    {
      id: 2,
      title: "Trường THPT Cầu Giấy",
      publisher:
        "Ng. 118 Đ. Nguyễn Khánh Toàn, Khu đô thị mới, Cầu Giấy, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 15/02/2024",
      imageUrl:
        "data:image/jpeg;base64,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",
      href: "https://truongthptcaugiay.edu.vn/"
    },
    {
      id: 4,
      title: "Trường THPT Kim Liên",
      publisher: "1 Ng. 4C P. Đặng Văn Ngữ, Trung Tự, Đống Đa, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 29/01/2024",
      imageUrl:
        "data:image/jpeg;base64,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",
      href: "http://thptkimlien-hanoi.edu.vn/"
    },
    {
      id: 5,
      title: "Trường THPT Trần Phú",
      publisher: "8 P. Hai Bà Trưng, Tràng Tiền, Hoàn Kiếm, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl:
        "data:image/jpeg;base64,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",
      href: "https://thpttranphuhk.hanoi.edu.vn/"
    },
    {
      id: 5,
      title: "Trường THPT Việt Đức",
      publisher: "47 P. Lý Thường Kiệt, Trần Hưng Đạo, Hoàn Kiếm, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl:
        "data:image/png;base64,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",
      href: "https://c3vietduc.edu.vn/"
    }
  ];

  return (
    <section className="space-y-10 pb-[3rem] lg:space-y-20">
      <div className="container relative mx-auto !mt-0 space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="text-[24px] font-bold text-[#414B5B] md:text-[32px]">
            <p>Hệ sinh thái học tập sáng tạo cấp THPT</p>
            <p className="text-[16px] font-[500]  md:text-[18px]">
              Những yếu tố tạo lên hệ sinh thái học tập sáng tạo cấp THPT
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto !mt-[36px]">
        <div className="grid gap-[25px] md:grid-cols-2 md:flex-nowrap xl:grid-cols-3">
          {dataCart.map((item) => (
            <Link key={item.title} href={item?.link}>
              <div className="h-[355px] w-full overflow-y-auto rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] xl:overflow-visible">
                <div className="flex h-[125px] items-center justify-center">
                  <div className="aspect-square w-[88px] ">
                    <Image
                      src={item.imageUrl}
                      width={88}
                      height={88}
                      className="h-full w-full object-cover"
                      alt="img"
                    />
                  </div>
                </div>
                <div className="space-y-[8px] px-[2rem] text-center text-[#414B5B]">
                  <h3 className="text-[24px] font-bold">{item.title}</h3>
                  <p className={`font-[400]`}>{item.description}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
      <div className="min-h-[443px] bg-[#F8F2E9]">
        <div className="container mx-auto py-[28px]">
          <div className="flex flex-col justify-center gap-[20px]">
            <div className="space-y-[20px]  font-bold text-[#414B5B] md:text-[36px]">
              <p className="text-center">Hướng nghiệp cho học sinh THPT</p>
              <p className="text-[18px]">
                Hướng nghiệp cho học sinh THPT là một quá trình quan trọng nhằm
                giúp các em xác định sở thích, năng lực và mục tiêu nghề nghiệp
                trong tương lai. Hướng nghiệp cho học sinh THPT không chỉ là
                việc chọn nghề mà còn là giúp các em phát triển toàn diện. Bằng
                việc tham gia tích cực vào quá trình này, học sinh sẽ có được sự
                chuẩn bị tốt nhất cho tương lai của mình.
              </p>
            </div>
            <div className="pb-[47px] pt-[30px]">
              <div className="flex flex-wrap justify-around gap-[1rem]">
                {dataCart3.map((item) => (
                  <div
                    key={item.id}
                    className="flex  flex-col items-center gap-[10px] border border-white px-[10px] py-[20px]"
                  >
                    <div className="min-h-[75px] w-[75px]">
                      <Image
                        src={item.imageUrl}
                        width={75}
                        height={75}
                        alt="image"
                      />
                    </div>
                    <p
                      dangerouslySetInnerHTML={{ __html: item.title }}
                      className="text-center text-[13.9px] font-semibold text-[#414B5B]"
                    ></p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container relative mx-auto !mt-[54px] space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="space-y-[23px] font-bold text-[#414B5B] md:text-[32px]">
            <p>Mô hình giáo dục sáng tạo</p>
            <p className="text-[18px]  font-[500]">
              Danh sách các mô hình giáo dục cho học sinh THPT
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto !mt-[36px]">
        <div className="grid gap-[25px] md:grid-cols-2 md:flex-nowrap xl:grid-cols-3">
          {dataCart2.map((item) => (
            <div className="h-[217px] w-full overflow-y-auto rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] md:overflow-visible">
              <div className="h-[70%] rounded-lg">
                <div className="h-[143px] w-full">
                  <img
                    src={item.imageUrl}
                    width="88"
                    height="88"
                    className="h-full w-full rounded-t-lg object-cover"
                    alt="img"
                  />
                </div>
              </div>
              <div className="h-[30%] px-[13px]">
                <h3 className="line-clamp-2 text-[18px] font-[600]">
                  {item.title}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="relative flex h-[594px] items-center justify-center bg-[url('/high-school/banner2.jpg')] bg-cover bg-center py-[2rem]">
        {/* Overlay đen với độ mờ */}
        <div className="absolute inset-0 z-0 bg-black opacity-50"></div>

        {/* Nội dung */}
        <div className="relative z-10 h-full w-[95%] border-2 p-2 2xl:container md:w-[95%] lg:p-[4rem] xl:w-[95%]">
          <div className="space-y-4 text-white lg:w-[89%]">
            <h3 className="text-[28px] font-bold lg:text-4xl">
              HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP THPT
            </h3>
            <p className="text-[18px] lg:text-lg">
              Hệ sinh thái học tập sáng tạo cấp THPT là một mô hình giáo dục đa
              dạng, tích hợp các phương pháp, công cụ và môi trường học tập nhằm
              khuyến khích sự sáng tạo, tư duy phản biện và khả năng giải quyết
              vấn đề của học sinh.
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div key={article.id} className="border-t border-gray-300 pt-[8px]">
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-[13px] h-40 w-full object-cover"
              />
              <a
                onClick={() => (window.location.href = article.link)}
                className="line-clamp-1 cursor-pointer font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2 line-clamp-3 font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
      <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">
            Bảng xếp hạng các trường THPT
          </h1>
        </div>
        <div className=" mt-[2rem] h-[1px] bg-gray-300"></div>

        {articleList.map((article, index) => (
          <div key={article.id}>
            <div
              className="flex flex-col items-center gap-4 rounded-lg pb-[28px] pt-[33px]  lg:flex-row"
            >
              {/* Hình ảnh */}
              <div className="aspect-square h-[209px] min-w-[227.86px] max-w-[227.86px]">
                <Image
                  width={200}
                  height={200}
                  src={article.imageUrl}
                  alt={article.title}
                  className="h-full w-full rounded-md object-contain"
                />
              </div>

              {/* Thông tin bài viết */}
              <div className="flex w-full flex-col justify-between">
                <div>
                  <h2 className="text-xl font-bold text-[#414B5B]">
                    {article.title}
                  </h2>
                  <p className="mt-2 text-[#414B5B]">
                    <span className="font-semibold">{article.publisher}</span> •{" "}
                    {article.year}
                  </p>
                </div>

                {/* Nút Read More */}
                <div className="mt-4 flex justify-end">
                  <a
                    onClick={() => (window.location.href = article.href)}
                    className="cursor-pointer rounded-full border border-blue-500 px-4 py-2 text-blue-500 transition-all duration-300 hover:border-blue-800 hover:text-blue-800"
                  >
                    Xem chi tiết...
                  </a>
                </div>
              </div>
            </div>

            <div>
              {index !== articleList.length - 1 && (
                <div className="mx-4 mb-[0.5rem] mt-[1rem] h-[1px] bg-gray-300"></div>
              )}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
