"use client";

import { BsArrowDownShort } from "react-icons/bs";
import { FaRegCircleRight } from "react-icons/fa6";

import Image from "next/image";

import CarouselSpacing from "@/components/carousel/CarouselSpacing";

export default function MiddleSchoolHomePage() {
  // data.js
  const articles = [
    {
      id: 2,
      category: "Hà Nội nhân rộng mô hình giáo dục thông minh sử dụng trí tuệ nhân tạo",
      title:
        "Sáng 16/10, Sở GD&ĐT Hà Nội thí điểm mô hình giáo dục thông minh sử dụng trí tuệ nhân tạo tại trường PTCS Nguyễn Đình <PERSON> (quận Hai Bà Trưng). <PERSON>u thử nghiệm thành công tại trường PTCS Nguyễn Đình <PERSON> và một số trường họ<PERSON>,",
      imageUrl: "https://**********.vws.vegacdn.vn/UploadImages/haydung/hanoi/anhhaydung/10.10/9.10/z593730803465954b21e780cb16f837f9ecad065864d4c_17102024825.jpg?w=1130",
      link: "/middle-school/news/2"
    },
    {
      id: 1,
      category: "Hà Nội phát động Tuần lễ hưởng ứng học tập suốt đời năm 2024",
      title:
        "Sáng 2/10, tại Trường THCS Giảng Võ, quận Ba Đình, Sở GD&ĐT Hà Nội tổ chức lễ khai mạc và phát động Tuần lễ hưởng ứng học tập suốt đời năm 2024.",
      imageUrl: "https://cdn.giaoducthoidai.vn/images/95b90f63c7a8d187b8dd97e8a06f6abc8bc7b0578eee194e89522ee1ba4d132ba2ae417e533549245c1a6668661aa6b8/img-0329-7148-7944.jpg?w=1130",
      link: "/middle-school/news/1"
    },
    {
      id: 3,
      category: "Quận Ba Đình: 91 học sinh THCS đoạt giải Nhất kỳ thi Olympic cấp quận",
      title:
        "Tham dự kỳ thi Olympic các môn văn hoá lớp 6, 7, 8 cấp Trung học cơ sở (THCS) quận Ba Đình năm học 2023-2024 có 960 thí sinh đoạt giải, trong đó có 91 giải Nhất.",
      imageUrl: "https://laodongthudo.vn/stores/news_dataimages/2024/052024/20/13/0557215c40e5e1bbb8f420240520133844.jpg?rt=20240520133911",
      link: "/middle-school/news/3"
    },
    {
      id: 4,
      category: "Thầy trò trường THPT Trần Phú - Hoàn Kiếm tích cực tham gia các hoạt động kỉ niệm 70 năm Ngày Giải phóng Thủ đô.",
      title:
        "Hướng tới kỉ niệm 70 năm Ngày Giải phóng Thủ đô (10/10/1954 -10/10/2024), trường THPT Trần Phú- Hoàn Kiếm đã tổ chức nhiều hoạt động  nhằm  giáo dục lòng yêu nước, niềm tự hào về thủ đô",
      imageUrl: "https://**********.vws.vegacdn.vn/UploadImages/news//2024/hanoi/quachthanhson/quachthanhson/2024_10/11/01-tp_111020241452.jpg?w=1130",
      link: "/middle-school/news/4"
    }
  ];
  const dataCart = [
    {
      id: 1,
      title: "Môi trường học tập",
      description:
        "Tạo ra không gian học tập thân thiện, đầy cảm hứng, nơi học sinh có thể thoải mái thể hiện ý tưởng và cảm xúc của mình.",
      imageUrl: "/img/news/img7.png"
    },
    {
      id: 2,
      title: "Phương pháp dạy học",
      description:
        "Sử dụng các phương pháp giảng dạy khác nhau như học theo dự án, học tập trải nghiệm, hay học nhóm, giúp học sinh phát triển tư duy sáng tạo và kỹ năng giải quyết vấn đề.",
      imageUrl: "/img/news/img8.png"
    },
    {
      id: 3,
      title: "Phương pháp dạy học",
      description:
        "Phương pháp dạy học tích cực: Thay vì chỉ truyền đạt kiến thức một chiều, giáo viên sử dụng các phương pháp dạy học kích thích sự tham gia của học sinh như học theo dự án, học trải nghiệm, và trò chơi giáo dục.",
      imageUrl: "/img/news/img6.png"
    },
    {
      id: 4,
      title: "Khuyến khích hợp tác",
      description:
        "Tạo cơ hội cho học sinh làm việc nhóm, trao đổi ý tưởng, và cùng nhau giải quyết vấn đề, giúp phát triển kỹ năng giao tiếp và hợp tác.",
      imageUrl: "/img/news/img10.png"
    },
    {
      id: 5,
      title: "Đánh giá đa dạng",
      description:
        "Sử dụng các hình thức đánh giá phong phú, không chỉ dựa vào điểm số mà còn xem xét sự phát triển kỹ năng, thái độ và sự sáng tạo của học sinh.",
      imageUrl: "/img/news/img9.png"
    },
    {
      id: 6,
      title: "Liên kết với cộng đồng",
      description:
        "Khuyến khích học sinh tham gia vào các hoạt động ngoại khóa, kết nối với thế giới bên ngoài qua các dự án cộng đồng, giúp rèn luyện kỹ năng xã hội và nâng cao tinh thần trách nhiệm.",
      imageUrl: "/img/news/img13.png"
    }
  ];
  const dataCart2 = [
    {
      id: 1,
      title: "Học tập dựa trên dự án (Project-Based Learning - PBL)",
      imageUrl: "/middle-school/mohinh1.jpg"
    },
    {
      id: 2,
      title: "Học theo chủ đề liên môn",
      imageUrl: "/middle-school/mohinh2.jpg"
    },
    {
      id: 3,
      title: "Ứng dụng công nghệ thông tin",
      imageUrl: "/middle-school/mohinh3.jpg"
    },
    {
      id: 4,
      title: "Học tập theo trải nghiệm",
      imageUrl: "/middle-school/mohinh4.jpg"
    },
    {
      id: 52,
      title: "Khuyến khích phát triển phẩm chất cá nhân",
      imageUrl: "/middle-school/mohinh5.jpg"
    },
    {
      id: 53,
      title: "Dạy học cá thể hóa",
      imageUrl: "/middle-school/mohinh6.jpg"
    },
    {
      id: 54,
      title: "Phản hồi và đánh giá thường xuyên",
      imageUrl: "/middle-school/mohinh7.jpg"
    }
  ];
  const articleList = [
    {
      id: 1,
      title: "Trường THCS Nguyễn Du ",
      publisher:
        "44 - 46 Hàng Quạt",
      publisher1: null,
      year: "Ngày tham gia 21/01/2024",
      imageUrl: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR5YFoTvd4GK1ZYDYIDgjsfvF4lqzFWIyHPmA&s",
      href: "http://thcsnguyendu.hoankiem.edu.vn/"
    },
    {
      id: 2,
      title: "Trường THCS Giảng Võ",
      publisher:
        "1A P. Trần Huy Liệu, Giảng Võ, Ba Đình, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 15/02/2024",
      imageUrl: "data:image/jpeg;base64,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",
      href: "https://c2giangvo.badinh.edu.vn/"
    },
    {
      id: 4,
      title: "Trường THCS Ba Đình",
      publisher:
        "145 Đ. Hoàng Hoa Thám, Ngọc Hồ, Ba Đình, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 29/01/2024",
      imageUrl: "data:image/jpeg;base64,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",
      href: "https://c2badinh.badinh.edu.vn/"
    },
    {
      id: 5,
      title: "Trường THCS Đoàn Thị Điểm",
      publisher:
        "Cơ sở 1: số 48 Lưu Hữu Phước, Mỹ Đình 1, quận Nam Từ Liêm, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl: "/rank/mn-9.png",
      href: "http://c2chuvanan.edu.vn/"
    },
    {
      id: 5,
      title: "Trường THCS Hoàng Hoa Thám",
      publisher:
        "P. Vĩnh Phúc, Khu tập thể 7.2 ha, Ba Đình, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl: "data:image/jpeg;base64,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",
      href: "https://c2hoanghoatham.badinh.edu.vn/"
    }
  ];
  const dataTeachingMethods = [
    {
      id: 1,
      title: "Học qua trải nghiệm (Experiential Learning)",
      imageUrl: "/middle-school/phuongphap1.jpg"
    },
    {
      id: 2312,
      title: "Dạy học theo dự án (Project-based Learning)",
      imageUrl: "/middle-school/phuongphap2.jpg"
    },
    {
      id: 1123,
      title: "Học tập hợp tác (Cooperative Learning)",
      imageUrl: "/middle-school/phuongphap3.jpg"
    },
    {
      id: 1123,
      title: "Sử dụng công nghệ thông tin (ICT Integration)",
      imageUrl: "/middle-school/phuongphap4.jpg"
    },
    {
      id: 112344,
      title: "Dạy học Lấy người học làm trung tâm (Student-centered Learning)",
      imageUrl: "/middle-school/phuongphap5.jpg"
    },
    {
      id: 1555,
      title: "Tích hợp liên môn (Interdisciplinary Teaching)",
      imageUrl: "/middle-school/phuongphap6.jpg"
    },
    {
      id: 525351,
      title: "Phương pháp tình huống (Case-based Learning): ",
      imageUrl: "/middle-school/phuongphap7.jpg"
    },
    {
      id: 15234,
      title: "Sáng tạo nghệ thuật (Arts Integration)",
      imageUrl: "/middle-school/phuongphap8.jpg"
    }
  ];

  return (
    <section className="space-y-10 pb-[3rem] lg:space-y-20">
      <div className="container relative mx-auto !mt-0 space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="text-[24px] font-bold text-[#414B5B] md:text-[32px]">
            <p>Hệ sinh thái học tập sáng tạo cấp THCS</p>
            <p className="text-[16px] font-[500] md:text-[18px]">
              Những yếu tố tạo lên hệ sinh thái học tập sáng tạo cấp THCS
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto !mt-[36px]">
        <div className="grid gap-[25px] md:grid-cols-2 md:flex-nowrap xl:grid-cols-3">
          {dataCart.map((item) => (
            <div className="h-[355px] w-full overflow-y-auto rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] md:overflow-visible">
              <div className="flex h-[125px] items-center justify-center">
                <div className="aspect-square w-[88px] ">
                  <Image
                    src={item.imageUrl}
                    width={88}
                    height={88}
                    className="h-full w-full object-cover"
                    alt="img"
                  />
                </div>
              </div>
              <div className="space-y-[8px] px-[2rem] text-center text-[#414B5B]">
                <h3 className="text-[24px] font-bold">{item.title}</h3>
                <p className={`font-[400]`}>{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="md:min-h-[613px] bg-[#F8F2E9]">
        <div className="container mx-auto py-[28px]">
          <div className="flex flex-col justify-center gap-[20px]">
            <div className="flex items-center  justify-center gap-2 font-bold text-[#414B5B] cursor-pointer" onClick={() => (window.location.href = "/middle-school/teaching-methods")}>
              <div className="relative text-[24px]">
                Phương pháp dạy học
                <div className="absolute right-[-2.9rem] top-0">
                  <Image
                    src="/icon/icon-arrow.svg"
                    width={40}
                    height={40}
                    alt="img"
                  />
                </div>
              </div>
            </div>
            <div className="md:pb-[39px] md:pt-[25px]">
              <CarouselSpacing data={dataTeachingMethods} />
            </div>
          </div>
        </div>
      </div>
      <div className="container relative mx-auto !mt-[54px] space-y-6 pt-6 lg:!px-0">
        <div className=" flex items-center justify-center  text-center">
          <div className="space-y-[23px] font-bold text-[#414B5B] md:text-[32px]">
            <p>Mô hình giáo dục sáng tạo</p>
            <p className="text-[18px]  font-[500]">
              Danh sách các mô hình giáo dục cho học sinh THCS
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto !mt-[36px]">
        <div className="grid gap-[25px] md:grid-cols-2 md:flex-nowrap xl:grid-cols-3">
          {dataCart2.map((item) => (
            <div className="h-[217px] w-full overflow-y-auto rounded-[20px] shadow-[0px_1px_4px_0px_#00000040] md:overflow-visible">
              <div className="h-[70%] rounded-lg">
                <div className="h-[143px] w-full">
                  <img
                      src={item.imageUrl}
                      width="88"
                      height="88"
                      className="h-full w-full rounded-t-lg object-cover"
                      alt="img"
                  />
                </div>
              </div>
              <div className="h-[30%] px-[13px]">
                <h3 className="line-clamp-2 text-[18px] font-[600]">
                  {item.title}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="relative flex h-[594px] items-center justify-center bg-[url('/middle-school/banner2.png')] bg-cover bg-center py-[2rem]">
        {/* Overlay đen với độ mờ */}
        <div className="absolute inset-0 z-0 bg-black opacity-50"></div>

        {/* Nội dung */}
        <div className="relative z-10 h-full w-[95%] border-2 p-2 2xl:container md:w-[95%] lg:p-[4rem] xl:w-[95%]">
          <div className="space-y-4 text-white lg:w-[89%]">
            <h3 className="text-[28px] font-bold lg:text-4xl">
              HỆ SINH THÁI HỌC TẬP SÁNG TẠO CẤP THCS
            </h3>
            <p className="text-[18px] lg:text-lg">
              Hệ sinh thái học tập sáng tạo cấp THCS (Trung học cơ sở) là một mô
              hình giáo dục nhằm thúc đẩy sự sáng tạo, tư duy độc lập và kỹ năng
              hợp tác của học sinh trong độ tuổi từ 11 đến 15. Mô hình này không
              chỉ tập trung vào việc tiếp thu kiến thức mà còn khuyến khích học
              sinh tham gia vào quá trình học tập một cách chủ động và tích cực.
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div key={article.id} className="border-t border-gray-300 pt-[8px]">
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-[13px] h-40 w-full object-cover"
              />
              <a
                onClick={() => (window.location.href = article.link)}
                className="line-clamp-1 cursor-pointer font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2 line-clamp-3 font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>
      </div>
      <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold text-[#414B5B]">
            Bảng xếp hạng các trường THCS
          </h1>
        </div>
        <div className=" mt-[2rem] h-[1px] bg-gray-300"></div>

        {articleList.map((article, index) => (
          <div key={article.id}>
            <div
              className="flex flex-col items-center gap-4 rounded-lg pb-[28px] pt-[33px]  lg:flex-row"
            >
              {/* Hình ảnh */}
              <div className="aspect-square h-[209px] min-w-[227.86px] max-w-[227.86px]">
                <Image
                  width={200}
                  height={200}
                  src={article.imageUrl}
                  alt={article.title}
                  className="h-full w-full rounded-md object-contain"
                />
              </div>

              {/* Thông tin bài viết */}
              <div className="flex w-full flex-col justify-between">
                <div>
                  <h2 className="text-xl font-bold text-[#414B5B]">
                    {article.title}
                  </h2>
                  <p className="mt-2 text-[#414B5B]">
                    <span className="font-semibold">{article.publisher}</span> •{" "}
                    {article.year}
                  </p>
                </div>

                {/* Nút Read More */}
                <div className="mt-4 flex justify-end">
                  <a
                    onClick={() => (window.location.href = article.href)}
                    className="cursor-pointer rounded-full border border-blue-500 px-4 py-2 text-blue-500 transition-all duration-300 hover:border-blue-800 hover:text-blue-800"
                  >
                    Xem chi tiết...
                  </a>
                </div>
              </div>
            </div>

            <div>
              {index !== articleList.length - 1 && (
                <div className="mx-4 mb-[0.5rem] mt-[1rem] h-[1px] bg-gray-300"></div>
              )}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
