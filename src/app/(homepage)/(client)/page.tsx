"use client";

import { FaAngleDown, FaCircleArrowRight } from "react-icons/fa6";
import { BsArrowDownShort } from "react-icons/bs";
import { FaQuoteRight } from "react-icons/fa";
import { useState } from "react";
import Link from "next/link";
import Image from "next/image";

import HeroSection from "@/components/ui/HeroSection";
import CardList from "@/components/card/CardList";
import Card from "@/components/card/Card";
import PuzzleChart from "@/components/chart/PuzzleChart";

export default function HomePage() {
  const [isActive, setIsActive] = useState<number | null>(null);

  const dataMenu = [
    {
      title: "Mầm non",
      href: "/",
      child: []
    },
    {
      title: "Tiểu học",
      href: "/"
      // child: [
      //   { title: "Creativity and Cities", href: "/" },
      //   { title: "Mission Statement", href: "/" }
      // ]
    },
    {
      title: "THCS",
      href: "/"
      // child: [
      //   { title: "Annual Conference", href: "/" },
      //   { title: "Mission Statement", href: "/" },
      //   { title: "Global Calls", href: "/" },
      //   { title: "UNESCO Cities Platform", href: "/" }
      // ]
    },
    {
      title: "THPT",
      href: "/",
      child: []
    }
  ];

  const dataCard = [
    {
      title: "Chủ thể học tập sáng tạo",
      description: "Học sinh, giáo viên và cha mẹ.",
      backgroundImageUrl: "vanhoa.jpg"
    },
    {
      title: "Tri thức học tập sáng tạo",
      description:
        "Kỹ năng giải quyết vấn đề, tri thức cá nhân, kiến thức khoa học, kỹ năng giao tiếp, tư duy phản biện và sáng tạo.",
      backgroundImageUrl: "trithuc.jpg"
    },
    {
      title: "Công nghệ học tập sáng tạo",
      description:
        "Học Tập Tương Tác khuyến khích sự tham gia tích cực qua hoạt động nhóm và thảo luận",
      backgroundImageUrl: "congnghe.jpg"
    },
    {
      title: "Bối cảnh học tập sáng tạo",
      description:
        "Không gian học tập sáng tạo, phương pháp dạy học sáng tạo và dự án thực tế phát triển tư duy và kỹ năng.",
      backgroundImageUrl: "boicanh.jpg"
    }
  ];
  const dataCard1 = [
    {
      title: "Văn hóa",
      description: "Văn hóa học tập sáng tạo",
      backgroundImageUrl: "chuthehoctap.jpg"
    },
    {
      title: "Thể chế",
      description: "Văn bản điều hành các cấp",
      backgroundImageUrl: "theche.jpg"
    },
    {
      title: "Chiến lược",
      description: "Chiến lược phát triển các cấp",
      backgroundImageUrl: "chienluoc.jpg"
    }
  ];

  const handleSetActive = (index: number) => {
    setIsActive(isActive === index ? null : index);
  };

  // data.js
  const articles = [
    {
      id: 2,
      category: "Hội thảo khoa học về hệ sinh thái ...",
      title:
        'Chiều 17/9, Trường Đại học Thủ đô Hà Nội đã tổ chức Hội thảo khoa học với chủ đề "Hệ sinh thái học tập...',
      imageUrl: "/news/news1.jpg"
    },
    {
      id: 1,
      category: "Thông báo tuyển sinh cùng lúc hai ...",
      title:
        "Thông báo tuyển sinh cùng lúc hai chương trình trình độ đại học hệ chính quy năm 2024 tại Trường Đại học...",
      imageUrl: "/news/news2.jpg"
    },
    {
      id: 3,
      category: "Thông báo tuyển sinh trình độ Đại ...",
      title:
        "Thông báo tuyển sinh trình độ Đại học theo hình thức Vừa làm vừa học nhóm ngành đào tạo giáo viên đối t...",
      imageUrl: "/news/news3.jpg"
    },
    {
      id: 4,
      category: "Thông báo đăng ký hưởng chính ...",
      title:
        "Thông báo đăng ký hưởng chính sách theo Nghị định 116/2020/NĐ-CP đối với sinh viên đại học chính quy ...",
      imageUrl: "/news/news4.jpg"
    }
  ];

  const articleList = [
    {
      id: 1,
      title: "Trường THPT Cầu Giấy",
      publisher:
        "Ng. 118 Đ. Nguyễn Khánh Toàn, Khu đô thị mới, Cầu Giấy, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 21/01/2024",
      imageUrl: "/rank/thpt1.jpg",
      href: "https://truongthptcaugiay.edu.vn"
    },
    {
      id: 2,
      title: "Trường THCS Hà Nội - Amsterdam",
      publisher: "Số 1 Hoàng Minh Giám, quận Cầu Giấy, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 15/02/2024",
      imageUrl: "/rank/thpt2.png",
      href: "https://hn-ams.edu.vn"
    },
    {
      id: 3,
      title: "Trường THCS dân lập Lương Thế Vinh",
      publisher: "Cơ sở 1: C5 Nam Trung Yên, quận Cầu Giấy, Hà Nội",
      publisher1: "Cơ sở 2: Thôn Yên Xá, xã Tân Triều, huyện Thanh Trì, Hà Nội",
      year: "Ngày tham gia 28/04/2024",
      imageUrl: "/rank/thpt3.jpg",
      href: "https://luongthevinh.com.vn/home/"
    },
    {
      id: 4,
      title: "Trường THCS Đoàn Thị Điểm",
      publisher:
        "Cơ sở 1: số 48 Lưu Hữu Phước, Mỹ Đình 1, quận Nam Từ Liêm, Hà Nội",
      publisher1: "Cơ sở 2: Khu độ thị Bắc Cổ Nhuế, quận Bắc Từ Liêm, Hà Nội",
      year: "Ngày tham gia 29/01/2024",
      imageUrl: "/rank/thpt4.jpg",
      href: "https://thcs-doanthidiem.edu.vn/"
    },
    {
      id: 5,
      title: "Trường THCS Chu Văn An",
      publisher: "Số 17 Thụy Khuê, quận Tây Hồ, Hà Nội",
      publisher1: null,
      year: "Ngày tham gia 20/01/2024",
      imageUrl: "/rank/thpt5.jpg",
      href: "http://c2chuvanan.edu.vn/"
    }
  ];

  const dataCard2 = [
    {
      title: "Mầm non",
      href: "/kinder-garten",
      img: "/icon/icon-1.png"
    },
    {
      title: "Tiểu học",
      href: "/elementary",
      img: "/icon/icon-2.png"
    },
    {
      href: "/middle-school",
      title: "THCS",
      img: "/icon/icon-3.png"
    },
    {
      href: "/high-school",
      title: "THPT",
      img: "/icon/icon-4.png"
    },
    {
      href: "/",
      title: "Trường",
      img: "/icon/icon-5.png"
    },
    {
      href: "/",
      title: "Quận Huyện",
      img: "/icon/icon-6.png"
    },
    {
      href: "/",
      title: "Thành phố",
      img: "/icon/icon-7.png"
    },
    {
      href: "/",
      title: "Quốc gia",
      img: "/icon/icon-8.png"
    }
  ];
  const dataCard3 = [
    {
      title: "Ứng dụng quản trị",
      href: "/user/resource/-1",
      description: "Cơ sở dữ liệu ngành giáo dục và đào tạo Hà Nội.",
      img: "/icon/icon-9.png"
    },
    {
      title: "Ứng dụng dạy học",
      href: "/user/resource/0",
      description:
        "Hệ thống chấm điểm và phân loại nhân sự hàng tháng tại các cơ quan Hà Nội.",
      img: "/icon/icon-10.png"
    }
  ];

  return (
    <section className="space-y-10 pb-[3rem] lg:space-y-20">
      <HeroSection
        description="Hệ sinh thái học tập sáng tạo Hà Nội kết nối, thúc đẩy sự phát triển bền vững của mạng lưới giáo dục sáng tạo hướng tới xây dựng Hà Nội - Thành phố sáng tạo."
        title="HỆ SINH THÁI HỌC TẬP SÁNG TẠO HÀ NỘI "
        backgroundImageUrl="bannermain_1.jpg"
      />
      <div className="container relative mx-auto !mt-0 space-y-6 pt-6 lg:!px-0">
        {/* <div className="mb-[5rem] flex flex-wrap gap-8 lg:h-[424px] lg:flex-nowrap">
          <CardList dataCard={dataCard} />
        </div>
        <div className="border-t pt-6">
          <Card dataCard={dataCard1} />
        </div> */}
        <div className=" flex items-center justify-center">
          <div className="font-bold text-[#0D15D1] md:text-[32px] text-[24px] text-center">
            Hanoi Creative Learning Ecosystem
          </div>
        </div>
        <div className="relative flex flex-wrap gap-[3.7rem] xl:flex-nowrap">
          <div className="h-[310px] md:h-[35rem] lg:h-[610px]">
            <div className="absolute right-1/2 translate-x-[50%] lg:relative">
              <PuzzleChart />
            </div>
          </div>
          <div className="flex w-full flex-col items-start gap-[27px] text-[#414B5B]">
            <p className="w-full border-b-[5px] border-[#F8801D] pb-2 text-[24px] font-bold text-[#F8801D]">
              Cấp học
            </p>
            <div className="flex w-full flex-wrap justify-between gap-x-[10px] gap-y-[14px]">
              {dataCard2.splice(0, 4).map((item, index) => (
                <Link
                  key={index + item.title}
                  href={item.href}
                  className="flex h-[42px] md:w-[49%] w-full items-center justify-start px-[18px] gap-1 rounded-[20px] bg-[#F8F2E9]"
                >
                  <Image src={item.img} width={24} height={24} alt={item.img} />
                  <p className="text-[22px] font-bold">{item.title}</p>
                </Link>
              ))}
            </div>
            <p className="w-full border-b-[5px] border-[#0094FF] pb-2 text-[24px] font-bold text-[#0094FF]">
              Phạm vi
            </p>
            <div className="flex w-full flex-wrap justify-between gap-x-[10px] gap-y-[14px]">
              {dataCard2.splice(0, 4).map((item, index) => (
                <Link
                  key={index + item.title}
                  href={item.href}
                  className="flex h-[42px] md:w-[49%] w-full items-center justify-start px-[18px] gap-1 rounded-[20px] bg-[#DCE8F1]"
                >
                  <Image src={item.img} width={24} height={24} alt={item.img} />
                  <p className="text-[22px] font-bold">{item.title}</p>
                </Link>
              ))}
            </div>
            <p className="w-full border-b-[5px] border-[#3BA459] pb-2 text-[24px] font-bold text-[#3BA459]">
              HỆ SINH THÁI ỨNG DỤNG SỐ TRONG GIÁO DỤC
            </p>
            <div className="flex w-full flex-wrap justify-between gap-x-[10px] gap-y-[14px]">
              {dataCard3.map((item, index) => (
                <Link
                  key={index + item.title}
                  href={item.href}
                  className="flex h-[42px] w-full items-center justify-start px-[18px] gap-1 rounded-[20px] bg-[#EFFBF2]"
                >
                  <Image src={item.img} width={24} height={24} alt={item.img} />
                  <p className="text-[22px] font-bold">{item.title}</p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
      <div className="relative flex h-[594px] items-center justify-center bg-[url('/mangluoi_1.jpg')] bg-cover bg-center py-[2rem]">
        {/* Overlay đen với độ mờ */}
        <div className="absolute inset-0 z-0 bg-black opacity-50"></div>

        {/* Nội dung */}
        <div className="relative z-10 h-full 2xl:container xl:w-[80%] md:w-[95%] w-[95%] border-2 p-2 lg:p-[4rem]">
          <div className="space-y-4 text-white lg:w-[50%]">
            <h3 className="text-[28px] font-bold lg:text-4xl">
              Hệ sinh thái học tập và sáng tạo là gì?
            </h3>
            <p className="text-[18px] lg:text-lg">
              Một môi trường học tập đa dạng và linh hoạt, nơi mà các yếu tố như
              người học, giáo viên, công nghệ, tài nguyên và cộng đồng kết nối,
              tương tác với nhau trên nền tảng văn hoá để thúc đẩy sự sáng tạo
              và đổi mới. Trong hệ sinh thái này, việc học không chỉ diễn ra
              trong lớp học mà còn mở rộng ra các không gian khác, khuyến khích
              sự hợp tác, chia sẻ ý tưởng và trải nghiệm thực tiễn.
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto !mt-[2rem] text-pretty font-[500] text-[#414B5B]">
        <div className="flex flex-col gap-4 space-y-4 md:space-y-0 lg:flex-row">
          <div className="relative">
            <h3 className="mb-2 text-[36px] font-bold ">
              Môi trường hệ sinh thái
            </h3>
            <div className=" flex h-full flex-col gap-4 text-[20px] leading-7 md:gap-0">
              <p className="h-[32%]">
                Hệ sinh thái học tập sáng tạo - Một môi trường học tập đa dạng
                và linh hoạt, nơi mà các yếu tố như người học, giáo viên, công
                nghệ, tài nguyên và cộng đồng kết nối, tương tác với nhau trên
                nền tảng văn hoá để thúc đẩy sự sáng tạo và đổi mới. Trong hệ
                sinh thái học tập sáng tạo, việc học không chỉ diễn ra trong lớp
                học mà còn mở rộng ra các không gian khác, khuyến khích sự hợp
                tác, chia sẻ ý tưởng và trải nghiệm thực tiễn.
              </p>
              <div className="h-[394px] w-full lg:max-w-[658px]">
                <Image
                  className="h-full w-full object-cover"
                  src={"/homepage/img-2.svg"}
                  width={200}
                  height={200}
                  alt="img"
                />
              </div>
              <Image
                className="absolute hidden md:block -right-10 top-[.5rem]"
                src={"/homepage/dot.svg"}
                width={45}
                height={51.99}
                alt="img"
              />
            </div>
          </div>
          <div className="h-full">
            <div className=" flex h-full flex-col-reverse items-center justify-between font-[600] md:flex-col md:items-end">
              <div className="h-[24rem] w-full lg:w-[80%]">
                <Image
                  className="h-full w-full object-cover"
                  src={"/homepage/img-1.svg"}
                  width={200}
                  height={200}
                  alt="img"
                />
              </div>
              <div className="relative">
                <h3 className="mb-2 text-[36px] font-bold ">
                  Mục tiêu hệ sinh thái
                </h3>
                <ul className="list-disc space-y-2">
                  <li>
                    Nâng cao chất lượng giáo dục toàn diện ở các cấp học, trong
                    đó nhấn mạnh việc phát triển năng lực học tập, sáng tạo của
                    học sinh, đáp ứng yêu cầu xây dựng thành phố Hà Nội sáng tạo
                  </li>
                  <li>
                    Tạo lập môi trường thuận lợi để đồng bộ các giải pháp nâng
                    cao chất lượng giáo dục, đào tạo ở các bậc học dựa trên khai
                    thác nền tảng công nghệ số nhằm thực hiện các mục tiêu đổi
                    mới căn bản toàn diện giáo dục đổi mới phương thức tổ chức
                    hoạt động của các trường học trong một hệ sinh thái chung,
                    lấy trường học làm trung tâm; góp phần xây dựng Hà Nội thành
                    đô thị thông minh bền vững
                  </li>
                </ul>
                <Image
                  className="absolute -right-10 top-[.5rem] hidden md:block"
                  src={"/homepage/dot.svg"}
                  width={45}
                  height={51.99}
                  alt="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <div className="container mx-auto">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] mb-[2rem]">
          <a href="#" className="flex items-center space-x-2 rounded-md text-black hover:text-blue-500 transition-colors duration-300 group">
            <span className="bg-blue-500 text-white w-[25px] h-[25px] rounded-full flex items-center justify-center text-[12px] transition-transform duration-300 transform group-hover:translate-x-1">➔</span>
            <span>Learn more</span>
          </a>
          <div className="mt-[20px]">
            <a href="#" className="flex items-center space-x-2 rounded-md text-black hover:text-blue-500 transition-colors duration-300 group">
              <span className="bg-blue-500 text-white w-[25px] h-[25px] rounded-full flex items-center justify-center text-[12px] transition-transform duration-300 transform group-hover:translate-x-1">➔</span>
              <span>UCCN Mission Statement</span>
            </a>
          </div>
        </div>
      </div> */}
      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Tin tức</h1>
        </div>

        <div className="mt-[40px] grid grid-cols-1 gap-x-[30px] md:grid-cols-2 lg:grid-cols-4">
          {articles.map((article) => (
            <div
              key={article.id}
              className="border-t border-gray-300 pt-[20px]"
            >
              <img
                src={article.imageUrl}
                alt={article.title}
                className="mb-4 h-40 w-full object-cover"
              />
              <a
                onClick={() =>
                  (window.location.href = "/user/news/" + article.id)
                }
                className="cursor-pointer font-medium text-[#414B5B] hover:text-blue-500"
              >
                {article.category}
              </a>
              <h3 className="mt-2  font-bold text-[#414B5B] transition-colors duration-300">
                {article.title}
              </h3>
            </div>
          ))}
        </div>

        <div className="mb-[4rem] mt-[50px] flex items-center justify-center">
          <button className="flex items-center space-x-2 rounded-full border border-gray-400 px-[20px] py-[7px] text-gray-500 transition-all duration-300 hover:border-gray-700 hover:text-gray-700">
            <span>Xem thêm</span>
            <span className="text-xl text-gray-400">
              <BsArrowDownShort />
            </span>
          </button>
        </div>

        <div className="h-[1px] bg-gray-300"></div>

        {/* <div className="flex justify-between items-center mt-[2rem]">
          <div className="w-3/5">
            <p className="text-lg">
             Mô hình "Hệ sinh thái học tập, sáng tạo" giúp đẩy mạnh phát triển khoa học, công nghệ và đổi mới sáng tạo trên địa bàn thành phố Hà Nội.
            </p>
          </div>

          <div className="flex items-center space-x-4">

            <div>
              <div className="w-[40px] mb-[10px] h-[40px] flex justify-center items-center border border-gray-400 rounded-full">
                <FaQuoteRight className="text-black text-lg" />
              </div>
              <div>
                <p className="font-bold text-base">Ernesto Ottone R.,</p>
                <p className="text-sm">Assistant Director-General for Culture of UNESCO</p>
              </div>
            </div>
          </div>
        </div> */}
      </div>

      <div className="relative flex h-[880px] items-center justify-center bg-[url('/anhhanoi.jpg')] bg-cover bg-fixed bg-center py-[2rem] lg:h-[550px]">
        {/* Overlay đen với độ mờ */}
        <div className="absolute inset-0 z-0 bg-black opacity-50"></div>

        {/* Nội dung */}
        <div className="relative z-10 flex h-full items-center justify-around bg-opacity-50 lg:p-[4rem]">
          <div className="2xl:container xl:w-[80%] md:w-[95%] w-[95%] space-y-4 text-white">
            <h3 className="text-4xl font-bold">
              Mô hình "Hệ sinh thái học tập, sáng tạo" có thể đánh giá các
              trường dựa trên bộ công cụ đánh giá theo từng cấp.
            </h3>
            <p className="text-lg">
              Bộ công cụ đánh giá các trường trong mô hình "Hệ sinh thái học
              tập, sáng tạo" được thiết kế để thúc đẩy sự phát triển khoa học,
              công nghệ và đổi mới sáng tạo tại Hà Nội. Bộ công cụ này bao gồm
              các tiêu chí và chỉ số đánh giá nhằm đo lường hiệu quả của các
              trường học trong việc triển khai các hoạt động sáng tạo và công
              nghệ.
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto text-[#414B5B]">
        <div className="h-[1px] bg-gray-300"></div>
        <div className="mt-[20px] ">
          <h1 className="text-4xl font-bold">Bảng xếp hạng các trường </h1>
        </div>
        <div className="mb-[2rem] mt-[2rem] h-[1px] bg-gray-300"></div>

        {articleList.map((article, index) => (
          <div key={article.id}>
            <div
              className="mb-6 flex flex-col items-center gap-4 rounded-lg p-4  lg:flex-row"
            >
              {/* Hình ảnh */}
              <div className="aspect-square w-[200px]">
                <img
                  src={article.imageUrl}
                  alt={article.title}
                  className="h-full w-full rounded-md object-cover"
                />
              </div>

              {/* Thông tin bài viết */}
              <div className="flex w-full flex-col justify-between">
                <div>
                  <h2 className="text-xl font-bold">{article.title}</h2>
                  <p className="mt-2 text-gray-600">
                    <span className="font-semibold">{article.publisher}</span> •{" "}
                    {article.year}
                  </p>
                </div>

                {/* Nút Read More */}
                <div className="mt-4 flex justify-end">
                  <a
                    onClick={() => (window.location.href = article.href)}
                    className="cursor-pointer rounded-full border border-blue-500 px-4 py-2 text-blue-500 transition-all duration-300 hover:border-blue-800 hover:text-blue-800"
                  >
                    Xem chi tiết...
                  </a>
                </div>
              </div>
            </div>

            <div>
              {index !== articleList.length - 1 && (
                <div className="mx-4 mb-[0.5rem] mt-[1rem] h-[1px] bg-gray-300"></div>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="container mx-auto !mt-[2rem]">
        <div className="w-full text-balance rounded-md bg-[#a6ddcb] px-4 py-8 leading-7 lg:w-[60%]">
          Bộ công cụ đánh giá các trường trong mô hình "Hệ sinh thái học tập,
          sáng tạo" nhằm mục tiêu nâng cao năng lực phát triển khoa học, công
          nghệ và đổi mới sáng tạo ở Hà Nội. Công cụ này bao gồm các chỉ số đo
          lường khả năng sáng tạo của học sinh, mức độ áp dụng công nghệ trong
          giảng dạy, sự hợp tác với cộng đồng và doanh nghiệp, cũng như hiệu quả
          của chương trình đào tạo. Bằng cách áp dụng bộ công cụ này, các trường
          học có thể nhận diện được những lĩnh vực cần cải thiện, qua đó tạo ra
          môi trường học tập tốt hơn và thúc đẩy sự phát triển bền vững cho
          thành phố.
        </div>
      </div>
    </section>
  );
}
