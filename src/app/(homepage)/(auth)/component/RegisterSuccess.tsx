'use client';
import { MdMarkEmailRead } from "react-icons/md";
import Loading from "@/components/ui/Loading";
import React from "react";
import {Button} from "@/components/ui/button";
const ResetPasswordSuccess = () => {
    return (
            <div
                className="flex flex-col py-8 items-center justify-center success-animation"
            >
                <div
                    className="w-20 h-20 rounded-full bg-blue-100 flex items-center justify-center mb-4 success-icon"
                >
                    <MdMarkEmailRead className="text-blue-500 text-4xl" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 text-center mb-4">
                    Vui lòng xác nhận email
                </h2>
                <h6 className=" text-center mb-6">
                    chúng tôi đã gửi email xác nhận đến địa chỉ email của bạn. Vui lòng kiểm tra hộp thư đến
                    và nhấp vào liên kết xác nhận để hoàn tất quá trình đăng ký.
                </h6>
                <div className="w-full max-w-xs">
                    <a
                        href="/login"
                    >
                        <Button
                            type="submit"
                            className={`w-full font-medium
                             py-3 px-4 rounded-md transition-colors !rounded-button whitespace-nowrap bg-primary text-white
                            }`}
                        >
                            Đăng nhập
                        </Button>
                    </a>
                </div>
            </div>
    );
};

export default ResetPasswordSuccess;