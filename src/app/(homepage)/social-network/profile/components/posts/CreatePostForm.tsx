"use client";

import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { PrivacyLevel } from "@/services/social-network/types/types";
import Image from "next/image";
import { useState } from "react";
import { toast } from "react-toastify";
import { UserData } from "../../page";
import HashtagInput from "./HashtagInput";
import ImageUploader from "./ImageUploader";
import PrivacySelector from "./PrivacySelector";

interface CreatePostFormProps {
  userData: UserData;
  resetList: () => void;
}

const CreatePostForm = ({ userData, resetList }: CreatePostFormProps) => {
  const { uploadSocialNetworkFile } = useUploadFile();
  const [content, setContent] = useState("");
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [privacy, setPrivacy] = useState<PrivacyLevel>(PrivacyLevel.PUBLIC);
  const [images, setImages] = useState<string[]>([]);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!content.trim() || loading) return;
    setLoading(true);

    try {
      // Sử dụng hashtags trực tiếp từ state

      // Tải lên các file ảnh mới và lấy fileIds
      let fileIds: string[] = [];

      // Nếu có file mới, tải lên và lấy fileIds
      if (imageFiles.length > 0) {
        // Tải lên từng file và lấy fileId
        const uploadPromises = imageFiles.map((file) =>
          uploadSocialNetworkFile(file)
        );
        const uploadResults = await Promise.all(uploadPromises);

        // Lấy fileIds từ kết quả tải lên
        const newFileIds = uploadResults.map((result) => result.data);
        fileIds = [...newFileIds];
      }

      // Chuẩn bị dữ liệu để gửi
      const postData = {
        content: content.trim(),
        privacyPolicy: privacy,
        hashTags: hashtags,
        files: fileIds.length > 0 ? fileIds : undefined
      };

      // Tạo bài đăng mới
      await socialNetworkServices.createPostSocialNetwork(postData);
      toast.success("Đã đăng bài viết mới");
      resetForm();
      resetList();
    } catch (error) {
      console.error("Lỗi khi đăng bài viết:", error);
      toast.error("Không thể đăng bài viết");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setContent("");
    setHashtags([]);
    setImages([]);
    setImageFiles([]);
    setPrivacy(PrivacyLevel.PUBLIC);
  };

  return (
    <div className="mb-6 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-300 hover:shadow-lg">
      <div className="p-5">
        {/* Header với avatar và tên người dùng */}
        <div className="mb-4 flex items-center gap-3">
          <div className="h-10 w-10 overflow-hidden rounded-full">
            {userData.avatarUrl ? (
              <Image
                src={userData.avatarUrl}
                alt={
                  userData.fullName ||
                  `${userData.firstName} ${userData.lastName}`
                }
                fill
                className="rounded-full object-cover"
              />
            ) : (
              <DefaultAvatar
                name={
                  userData.fullName ||
                  `${userData.firstName} ${userData.lastName}`
                }
                size="100%"
              />
            )}
          </div>
          <div>
            <div className="font-medium text-gray-800">
              {userData.fullName ||
                `${userData.firstName} ${userData.lastName}`}
            </div>
            <div className="mt-1">
              <PrivacySelector privacy={privacy} onChange={setPrivacy} />
            </div>
          </div>
        </div>

        {/* Textarea nhập nội dung */}
        <div className="mb-4">
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Bạn đang nghĩ gì?"
            className="min-h-[120px] w-full resize-none rounded-lg border-0 bg-gray-50 p-4 text-gray-800 placeholder-gray-500 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          />
        </div>

        <HashtagInput hashtags={hashtags} onChange={setHashtags} />

        <ImageUploader
          images={images}
          onImagesChange={setImages}
          imageFiles={imageFiles}
          onImageFilesChange={setImageFiles}
          className="mb-3 mt-2"
        />

        {/* Footer với các nút chức năng */}
        <div className="mt-4 flex items-center justify-end gap-2 border-t border-gray-100 pt-4">
          <div className="flex items-center space-x-3">
            <button
              onClick={resetForm}
              className="flex items-center gap-2 rounded-full border border-gray-200 px-4 py-2 text-gray-600 transition-all hover:bg-gray-50 hover:text-gray-800"
              type="button"
            >
              <span className="font-medium">Hủy</span>
            </button>
          </div>

          <button
            onClick={handleSubmit}
            className="rounded-full bg-blue-600 px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-blue-700 hover:shadow-md disabled:opacity-70 disabled:hover:bg-blue-600 disabled:hover:shadow-none"
            disabled={!content.trim() || loading}
          >
            {loading ? (
              <span className="flex items-center gap-2">
                <svg className="h-4 w-4 animate-spin" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Đang đăng...
              </span>
            ) : (
              "Đăng"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreatePostForm;
