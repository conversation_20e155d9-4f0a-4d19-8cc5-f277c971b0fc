import DefaultAvatar from "@/components/ui/DefaultAvatar";
import useUploadFile from "@/hook/useUploadFile";
import friendServices from "@/services/social-network/friendServices";
import { FriendList } from "@/services/social-network/types/friend";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import type { UserData } from "../page";

const DEFAULT_SIZE = 10;

interface FriendsTabReadOnlyProps {
  userData: UserData;
}

const FriendsTabReadOnly = ({ userData }: FriendsTabReadOnlyProps) => {
   const router = useRouter();
  const [friends, setFriends] = useState<FriendList[]>([]);
  const [page, setPage] = useState(-1);
  const [numberOfFriend, setNumberOfFriend] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const observerTarget = useRef<HTMLDivElement>(null);
  const { viewFile } = useUploadFile();
  const [initUI, setInitUI] = useState(true);
    const [initialized, setInitialized] = useState(false);

  const fetchFriends = async (
    type: "reset" | "infinite",
    page: number = 0,
    size: number = DEFAULT_SIZE
  ) => {
    setLoading(true);
    try {
      // Sử dụng getListFriendByUsername thay vì getListFriendByCurrentSession
      const response = await friendServices.getListFriendByUsername({
        page,
        size,
        username: userData.email || ""
      });

      if (numberOfFriend == 0)
        setNumberOfFriend(response.data?.data?.totalElements);
      const list: FriendList[] = response.data?.data?.content ?? [];

      const listWithThumbnails: FriendList[] = await Promise.all(
        list.map(async (friend) => ({
          ...friend,
          thumbnail: await viewFile(friend.thumbnail, "social-network")
        }))
      );

      type === "infinite"
        ? setFriends([...friends, ...listWithThumbnails])
        : setFriends([...listWithThumbnails]);
      setHasMore(!response.data.data.last);
    } catch (error) {
      console.error("Lỗi khi tải danh sách bạn bè:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (userData.email && !initialized) {
      setInitialized(true);
      fetchFriends("reset").then(() => setInitUI(false));
    }
  }, [userData.email, initialized]);

  // Xử lý infinite scroll
  const loadMoreItems = useCallback(() => {
    if (loading || !hasMore || !userData.email) return;
    setPage(prevPage => {
      const nextPage = prevPage + 1;
      fetchFriends("infinite", nextPage);
      return nextPage;
    });
  }, [hasMore, loading, userData.email]);

  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    if (!initialized) return; // Chỉ setup observer sau khi đã initialized

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loading && hasMore) {
          loadMoreItems();
        }
      },
      { threshold: 0.1, rootMargin: '20px' }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMoreItems, initialized, loading, hasMore]);

  return (
    <div className="space-y-6">
      <div className="rounded-lg bg-white p-6 shadow-md">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-xl font-semibold">Bạn bè</h3>
          <span className="text-gray-600">{numberOfFriend} người</span>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {friends.map((friend, index) => (
            <div
              key={`${friend.username}-${index}`}
              className={`flex items-center gap-3 rounded-lg border border-gray-100 p-3 transition hover:bg-gray-50 ${userData.email === friend.username ? 'cursor-not-allowed opacity-70' : 'cursor-pointer'}`}
               onClick={()=>{if(userData.email !== friend.username){ router.push(`/social-network/profile/${friend.username}`)}}}
            >
              {friend.thumbnail ? (
                <Image
                  // @ts-ignore
                  src={friend.thumbnail}
                  alt={friend.firstName + " " + friend.lastName}
                  width={50}
                  height={50}
                  className="h-[50px] w-[50px] rounded-full object-cover"
                />
              ) : (
                <DefaultAvatar
                  name={`${friend.firstName} ${friend.lastName}` || "User"}
                  width={50}
                  height={50}
                  size={50}
                />
              )}
              <div className="flex-1">
                <div className="font-medium">
                  {friend.firstName + " " + friend.lastName}
                </div>
                <div className="text-sm text-gray-600">{friend.job}</div>
                {friend.mutualFriendCount > 0 ? (
                  <div className="text-sm text-gray-600">
                    {friend.mutualFriendCount} bạn chung
                  </div>
                ) : (
                  <></>
                )}
              </div>
              {/* Không hiển thị button action cho read-only */}
            </div>
          ))}
        </div>

        {/* Hiển thị khi không có bạn bè nào */}
        {friends.length === 0 && !loading && !initUI && (
          <div className="py-8 text-center">
            <div className="mb-2 text-gray-500">Chưa có bạn bè nào</div>
            <p className="text-gray-600">Người dùng này chưa có bạn bè.</p>
          </div>
        )}
      </div>

      {/* Loading indicator & observer target */}
      {hasMore && (
        <div ref={observerTarget} className="py-4 text-center">
          {loading ? (
            <div className="flex items-center justify-center space-x-2">
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "0ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "150ms" }}
              ></div>
              <div
                className="h-3 w-3 animate-bounce rounded-full bg-blue-600"
                style={{ animationDelay: "300ms" }}
              ></div>
            </div>
          ) : (
            <div className="text-sm text-gray-500">Kéo xuống để tải thêm</div>
          )}
        </div>
      )}
    </div>
  );
};

export default FriendsTabReadOnly;
