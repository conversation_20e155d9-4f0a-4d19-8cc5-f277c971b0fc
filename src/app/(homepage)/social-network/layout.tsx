import ClientSocialNetworkLayout from "@/components/ClientSocialNetworkLayout";
import { Metadata } from "next";
import { Quicksand } from "next/font/google";
import React from "react";

const quicksand = Quicksand({
  subsets: ["latin"]
});

export const metadata: Metadata = {
  title: "<PERSON><PERSON> sinh thái học tập, sáng tạo",
  description: "<PERSON>ệ sinh thái học tập, sáng tạo"
};

export default function SocialNetworkLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={`${quicksand.className}`}>
        <div className="min-h-screen bg-gray-50">
          <ClientSocialNetworkLayout />
          <main >
            {children}
          </main>
        </div>
      </body>
    </html>
  );
}
