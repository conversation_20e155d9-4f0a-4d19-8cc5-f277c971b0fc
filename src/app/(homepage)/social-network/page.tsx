'use client';

import { FaComment, FaFileAlt, FaSearch, FaThumbsUp } from 'react-icons/fa';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import Image from 'next/image';

// Dữ liệu mẫu cho người dùng hiện tại
const currentUser = {
  name: '<PERSON><PERSON>ễn <PERSON>n <PERSON>',
  avatar: 'https://i.pravatar.cc/300?img=1',
  bio: 'Giáo viên Tiếng Anh | Đam mê chia sẻ kiến thức và kết nối cộng đồng giáo dục',
  totalLikes: 245,
  totalPosts: 28,
  followers: 156,
  following: 89
};

// Dữ liệu mẫu cho các bài đăng
const generateMockPosts = (count: number, startId = 1) => {
  const users = [
    { name: '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>', avatar: 'https://i.pravatar.cc/300?img=1' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', avatar: 'https://i.pravatar.cc/300?img=2' },
    { name: '<PERSON><PERSON>', avatar: 'https://i.pravatar.cc/300?img=3' },
    { name: 'Phạm Thị D', avatar: 'https://i.pravatar.cc/300?img=4' },
    { name: 'Hoàng Văn E', avatar: 'https://i.pravatar.cc/300?img=5' }
  ];
  
  const contents = [
    'Đây là bài đăng về #giáodục và #họctập trong môi trường số. Rất mong nhận được ý kiến đóng góp từ mọi người!',
    'Chào mừng các bạn đến với diễn đàn trao đổi kiến thức! #UNESCO #sángtạo',
    'Hôm nay tôi đã tham gia hội thảo về phương pháp giảng dạy mới. Thật sự rất bổ ích! #phươngphápgiảngdạy',
    'Chia sẻ một số tài liệu học tập mới nhất về #khoahọcdữliệu mà tôi vừa tổng hợp được. Hy vọng giúp ích cho mọi người.',
    'Làm thế nào để tạo động lực học tập cho học sinh? Đây là câu hỏi mà nhiều giáo viên đang trăn trở. #độnglựchọctập',
    'Vừa hoàn thành khóa học về trí tuệ nhân tạo trong giáo dục. Có ai quan tâm đến chủ đề này không? #AI #giáodục',
    'Chia sẻ kinh nghiệm xây dựng cộng đồng học tập trực tuyến hiệu quả. #cộngđồnghọctập #trựctuyến',
    'Hôm nay tôi đã thử nghiệm phương pháp dạy học mới với học sinh và nhận được phản hồi tích cực! #đổimới #phươngphápdạyhọc'
  ];
  
  const timeAgo = [
    '5 phút trước', '10 phút trước', '15 phút trước', '30 phút trước',
    '1 giờ trước', '2 giờ trước', '3 giờ trước', '5 giờ trước',
    'Hôm qua', '2 ngày trước', '3 ngày trước', '1 tuần trước'
  ];
  
  // Mảng chứa các ảnh mẫu cố định
  const images = [
    'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1000&auto=format&fit=crop',  // Học sinh trong thư viện
    'https://images.unsplash.com/photo-1577896851231-70ef18881754?q=80&w=1000&auto=format&fit=crop',  // Trường học
    'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?q=80&w=1000&auto=format&fit=crop',  // Lớp học
    'https://images.unsplash.com/photo-1509062522246-3755977927d7?q=80&w=1000&auto=format&fit=crop',  // Giáo viên
    'https://images.unsplash.com/photo-1524178232363-1fb2b075b655?q=80&w=1000&auto=format&fit=crop',  // Sinh viên đang học
    'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?q=80&w=1000&auto=format&fit=crop',  // Nhóm học tập
    'https://images.unsplash.com/photo-1523240795612-9a054b0db644?q=80&w=1000&auto=format&fit=crop',  // Hội thảo
    'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=1000&auto=format&fit=crop',  // Công nghệ giáo dục
    'https://images.unsplash.com/photo-1513258496099-48168024aec0?q=80&w=1000&auto=format&fit=crop',  // Sách giáo dục
    'https://images.unsplash.com/photo-1571260899304-425eee4c7efc?q=80&w=1000&auto=format&fit=crop',  // Trẻ em học tập
    'https://images.unsplash.com/photo-1544717305-2782549b5136?q=80&w=1000&auto=format&fit=crop',  // Nghiên cứu khoa học
    'https://images.unsplash.com/photo-1511629091441-ee46146481b6?q=80&w=1000&auto=format&fit=crop',  // Thiết bị giáo dục
    'https://images.unsplash.com/photo-1610484826967-09c5720778c7?q=80&w=1000&auto=format&fit=crop',  // Giảng dạy trực tuyến
    'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?q=80&w=1000&auto=format&fit=crop',  // Làm việc trên máy tính
    'https://images.unsplash.com/photo-1523580494863-6f3031224c94?q=80&w=1000&auto=format&fit=crop',  // Thư viện
    ''
  ];
  
  return Array(count).fill(0).map((_, index) => {
    const id = startId + index;
    const randomUserIndex = Math.floor(Math.random() * users.length);
    const randomContentIndex = Math.floor(Math.random() * contents.length);
    const randomTimeIndex = Math.floor(Math.random() * timeAgo.length);
    // Đảm bảo chỉ có khoảng 70% bài viết có ảnh
    const hasImage = Math.random() < 0.7;
    const randomImageIndex = hasImage ? Math.floor(Math.random() * (images.length - 1)) : images.length - 1;
    const randomLikes = Math.floor(Math.random() * 100) + 1;
    const randomComments = Math.floor(Math.random() * 20);
    
    // Tạo danh sách bình luận ngẫu nhiên
    const commentsList = [];
    for (let i = 0; i < randomComments; i++) {
      const randomCommenterIndex = Math.floor(Math.random() * users.length);
      const randomCommentTimeIndex = Math.floor(Math.random() * timeAgo.length);
      commentsList.push({
        id: i + 1,
        user: users[randomCommenterIndex],
        content: `Bình luận thứ ${i + 1} cho bài viết này. #bìnhluận`,
        createdAt: timeAgo[randomCommentTimeIndex]
      });
    }
    
    return {
      id,
      user: users[randomUserIndex],
      createdAt: timeAgo[randomTimeIndex],
      content: contents[randomContentIndex],
      image: images[randomImageIndex],
      likes: randomLikes,
      comments: randomComments,
      commentsList
    };
  });
};

// Tạo dữ liệu mẫu cho bình luận gần đây
const recentComments = [
  {
    id: 1,
    postId: 3,
    user: { name: 'Hoàng Văn E', avatar: 'https://i.pravatar.cc/300?img=5' },
    content: 'Cảm ơn bạn đã chia sẻ thông tin hữu ích!',
    createdAt: '2 phút trước'
  },
  {
    id: 2,
    postId: 1,
    user: { name: 'Lê Văn C', avatar: 'https://i.pravatar.cc/300?img=3' },
    content: 'Tôi rất quan tâm đến chủ đề này. Có thể chia sẻ thêm tài liệu không?',
    createdAt: '5 phút trước'
  },
  {
    id: 3,
    postId: 5,
    user: { name: 'Phạm Thị D', avatar: 'https://i.pravatar.cc/300?img=4' },
    content: 'Đây đúng là vấn đề mà chúng ta cần thảo luận nhiều hơn!',
    createdAt: '10 phút trước'
  },
  {
    id: 4,
    postId: 2,
    user: { name: 'Trần Thị B', avatar: 'https://i.pravatar.cc/300?img=2' },
    content: 'Rất hữu ích, cảm ơn bạn nhiều!',
    createdAt: '15 phút trước'
  },
  {
    id: 5,
    postId: 4,
    user: { name: 'Nguyễn Văn A', avatar: 'https://i.pravatar.cc/300?img=1' },
    content: 'Tôi đã áp dụng phương pháp này và thấy hiệu quả rõ rệt.',
    createdAt: '20 phút trước'
  }
];

export default function FeedPage() {
  const [posts, setPosts] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchType, setSearchType] = useState('all'); // 'all', 'posts', 'users', 'hashtags'
  const observerTarget = useRef<HTMLDivElement>(null);

  // Tải dữ liệu ban đầu
  useEffect(() => {
    setPosts(generateMockPosts(10));
  }, []);

  // Xử lý infinite scroll
  const loadMorePosts = useCallback(() => {
    if (loading || !hasMore) return;
    
    setLoading(true);
    // Giả lập API call
    setTimeout(() => {
      const newPosts = generateMockPosts(5, posts.length + 1);
      setPosts(prev => [...prev, ...newPosts]);
      setPage(prev => prev + 1);
      setLoading(false);
      
      // Giả sử chỉ tải tối đa 50 bài viết
      if (page >= 10) {
        setHasMore(false);
      }
    }, 1000);
  }, [loading, hasMore, page, posts.length]);

  // Thiết lập Intersection Observer cho infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          loadMorePosts();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [loadMorePosts]);

  // Xử lý tìm kiếm
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Trong thực tế, đây sẽ là API call
    console.log(`Tìm kiếm: ${searchTerm} - Loại: ${searchType}`);
  };

  // Lọc bài viết theo từ khóa tìm kiếm
  const filteredPosts = posts.filter(post => {
    if (!searchTerm) return true;
    
    const lowerSearchTerm = searchTerm.toLowerCase();
    
    switch (searchType) {
      case 'posts':
        return post.content.toLowerCase().includes(lowerSearchTerm);
      case 'users':
        return post.user.name.toLowerCase().includes(lowerSearchTerm);
      case 'hashtags':
        return post.content.toLowerCase().includes(`#${lowerSearchTerm}`);
      default: // 'all'
        return (
          post.content.toLowerCase().includes(lowerSearchTerm) ||
          post.user.name.toLowerCase().includes(lowerSearchTerm) ||
          post.content.toLowerCase().includes(`#${lowerSearchTerm}`)
        );
    }
  });

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Thanh tìm kiếm */}
      <div className="mb-6 bg-white rounded-lg shadow p-4">
        <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder="Tìm kiếm bài viết, người dùng hoặc hashtag..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
          </div>
          <div className="flex gap-2">
            <select
              value={searchType}
              onChange={(e) => setSearchType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Tất cả</option>
              <option value="posts">Bài viết</option>
              <option value="users">Người dùng</option>
              <option value="hashtags">Hashtag</option>
            </select>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              Tìm kiếm
            </button>
          </div>
        </form>
      </div>

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Thông tin tài khoản - Cột trái */}
        <div className="lg:w-1/4 order-1">
          <div className="bg-white rounded-lg shadow p-5 sticky top-24">
            <div className="flex flex-col items-center text-center mb-4">
              <Image
                src={currentUser.avatar}
                alt={currentUser.name}
                width={100}
                height={100}
                className="rounded-full object-cover mb-3"
              />
              <h2 className="text-xl font-semibold">{currentUser.name}</h2>
              <p className="text-gray-600 text-sm mt-1">{currentUser.bio}</p>
            </div>
            
            <div className="border-t border-gray-200 pt-4 mt-4">
              <div className="flex justify-between mb-3">
                <div className="text-center">
                  <div className="text-lg font-semibold">{currentUser.totalPosts}</div>
                  <div className="text-xs text-gray-500">Bài viết</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{currentUser.totalLikes}</div>
                  <div className="text-xs text-gray-500">Lượt thích</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">{currentUser.followers}</div>
                  <div className="text-xs text-gray-500">Người theo dõi</div>
                </div>
              </div>
            </div>
            
            {/*<div className="border-t border-gray-200 pt-4 mt-4">*/}
            {/*  <h3 className="font-medium mb-3">Hashtags phổ biến</h3>*/}
            {/*  <div className="flex flex-wrap gap-2">*/}
            {/*    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">#giáodục</span>*/}
            {/*    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">#họctập</span>*/}
            {/*    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">#UNESCO</span>*/}
            {/*    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">#sángtạo</span>*/}
            {/*    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">#đổimới</span>*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        </div>

        {/* Bảng tin - Cột giữa */}
        <div className="lg:w-2/4 order-3 lg:order-2">
          <div className="space-y-6">
            {filteredPosts.length > 0 ? (
              filteredPosts.map((post) => (
                <div key={post.id} className="rounded-lg bg-white shadow p-5">
                  <div className="flex items-center gap-3 mb-3">
                    <Image 
                      src={post.user.avatar} 
                      alt={post.user.name} 
                      width={48} 
                      height={48} 
                      className="rounded-full object-cover" 
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{post.user.name}</div>
                      <div className="text-xs text-gray-500">{post.createdAt}</div>
                    </div>
                  </div>
                  
                  <div className="mb-4 text-gray-800 whitespace-pre-line">
                    {post.content.split(' ').map((word: string, i: number) => {
                      if (word.startsWith('#')) {
                        return (
                          <span key={i} className="text-blue-600 hover:underline cursor-pointer">
                            {word}{' '}
                          </span>
                        );
                      }
                      return word + ' ';
                    })}
                  </div>
                  
                  {post.image && (
                    <div className="mb-4">
                      <Image 
                        src={post.image} 
                        alt="post" 
                        width={600} 
                        height={340} 
                        className="rounded-lg w-full max-h-96 object-cover" 
                      />
                    </div>
                  )}
                  
                  <div className="flex items-center gap-6 mt-3 pb-3 border-b border-gray-100">
                    <button className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors duration-200">
                      <FaThumbsUp className="text-lg" />
                      <span>{post.likes} Thích</span>
                    </button>
                    <button className="flex items-center gap-1 text-gray-700 hover:text-blue-600 transition-colors duration-200">
                      <FaComment className="text-lg" />
                      <span>{post.comments} Bình luận</span>
                    </button>
                  </div>
                  
                  {/* Hiển thị bình luận đầu tiên nếu có */}
                  {post.commentsList && post.commentsList.length > 0 && (
                    <div className="mt-3 pt-2">
                      <div className="flex items-start gap-2">
                        <Image 
                          src={post.commentsList[0].user.avatar} 
                          alt={post.commentsList[0].user.name} 
                          width={32} 
                          height={32} 
                          className="rounded-full object-cover mt-1" 
                        />
                        <div className="bg-gray-100 rounded-lg p-2 flex-grow">
                          <div className="font-medium text-sm">{post.commentsList[0].user.name}</div>
                          <div className="text-sm">{post.commentsList[0].content}</div>
                          <div className="text-xs text-gray-500 mt-1">{post.commentsList[0].createdAt}</div>
                        </div>
                      </div>
                      
                      {post.comments > 1 && (
                        <button className="text-sm text-blue-600 hover:underline mt-2 ml-10">
                          Xem tất cả {post.comments} bình luận
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="bg-white rounded-lg shadow p-8 text-center">
                <div className="text-gray-500 mb-2">Không tìm thấy bài viết nào phù hợp</div>
                <button 
                  onClick={() => setSearchTerm('')}
                  className="text-blue-600 hover:underline"
                >
                  Xóa bộ lọc tìm kiếm
                </button>
              </div>
            )}
            
            {/* Loading indicator & observer target */}
            {hasMore && (
              <div ref={observerTarget} className="py-4 text-center">
                {loading ? (
                  <div className="flex justify-center items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">Kéo xuống để tải thêm</div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Bình luận gần đây - Cột phải */}
        <div className="lg:w-1/4 order-2 lg:order-3">
          <div className="bg-white rounded-lg shadow p-5 sticky top-24">
            <h3 className="font-semibold text-lg mb-4 flex items-center gap-2">
              <FaComment className="text-blue-600" />
              Bình luận gần đây
            </h3>
            
            <div className="space-y-4">
              {recentComments.map(comment => (
                <div key={comment.id} className="border-b border-gray-100 pb-3 last:border-0">
                  <div className="flex items-start gap-2">
                    <Image 
                      src={comment.user.avatar} 
                      alt={comment.user.name} 
                      width={36} 
                      height={36} 
                      className="rounded-full object-cover mt-1" 
                    />
                    <div>
                      <div className="font-medium text-sm">{comment.user.name}</div>
                      <div className="text-sm">{comment.content}</div>
                      <div className="flex justify-between items-center mt-1">
                        <span className="text-xs text-gray-500">{comment.createdAt}</span>
                        <button className="text-xs text-blue-600 hover:underline">Xem bài viết</button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/*<div className="mt-4 pt-2 border-t border-gray-200">*/}
            {/*  <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">*/}
            {/*    <FaFileAlt className="text-blue-600" />*/}
            {/*    Hoạt động*/}
            {/*  </h3>*/}
            {/*  <div className="space-y-2 text-sm">*/}
            {/*    <div className="flex items-center gap-2">*/}
            {/*      <div className="w-2 h-2 rounded-full bg-green-500"></div>*/}
            {/*      <span>5 bài viết mới hôm nay</span>*/}
            {/*    </div>*/}
            {/*    <div className="flex items-center gap-2">*/}
            {/*      <div className="w-2 h-2 rounded-full bg-blue-500"></div>*/}
            {/*      <span>12 người dùng đang hoạt động</span>*/}
            {/*    </div>*/}
            {/*    <div className="flex items-center gap-2">*/}
            {/*      <div className="w-2 h-2 rounded-full bg-purple-500"></div>*/}
            {/*      <span>3 chủ đề đang thảo luận sôi nổi</span>*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        </div>
      </div>
    </div>
  );
}

