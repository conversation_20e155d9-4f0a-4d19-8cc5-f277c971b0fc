import Axios from "../../lib/Axios";

const getListFriendByCurrentSession = ({
                                           page = 0,
                                           size = 10
                                       }: {
    page: number;
    size: number;
}) => Axios.getRequest(`/user/friend-social-network`, {page, size});

const getListFriendByUsername = ({
                                     page = 0,
                                     size = 10,
                                     username = ''
                                 }: {
    page: number;
    size: number;
    username: string;
}) => Axios.getRequest(`/user/friend-social-network/profile/${username}`, {page, size});

// Gửi lời mời kết bạn
const sendFriendRequest = (username: string) =>
    Axios.postRequest(`/user/friend-social-network/sending-request`, { username });

// Chấp nhận lời mời kết bạn
const acceptFriendRequest = (username: string) =>
    Axios.putRequest(`/user/friend-social-network/accept-request`, { username });

// Từ chối lời mời kết bạn
const rejectFriendRequest = (username: string) =>
    Axios.putRequest(`/user/friend-social-network/reject-request`, { username });


const FriendServices = {
    getListFriendByCurrentSession,
    getListFriendByUsername,
    sendFriendRequest,
    acceptFriendRequest,
    rejectFriendRequest
};

export default FriendServices;

