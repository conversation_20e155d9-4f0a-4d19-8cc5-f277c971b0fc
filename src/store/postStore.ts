import { create } from 'zustand';

// Định nghĩa interface cho store
interface PostStore {
  // Trạng thái để theo dõi khi nào cần reset danh sách bài đăng
  resetCounter: number;
  
  // Hàm để tăng resetCounter, kích hoạt việc reset danh sách
  triggerReset: () => void;
}

// Tạo store với Zustand
const usePostStore = create<PostStore>((set) => ({
  // Khởi tạo giá trị ban đầu
  resetCounter: 0,
  
  // Hàm để tăng resetCounter
  triggerReset: () => set((state) => ({ resetCounter: state.resetCounter + 1 })),
}));

export default usePostStore;
