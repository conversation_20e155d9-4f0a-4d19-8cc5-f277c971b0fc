import axios, {
    AxiosResponse,
    InternalAxiosRequestConfig,
    RawAxiosRequestHeaders,
    type AxiosInstance,
    type AxiosRequestConfig
} from "axios";
import {getCookie, deleteCookie} from "cookies-next";
import {CookieName} from "@/constants/cookie";

const axiosDefaultHeaderOption = {
    "cache-control": "public, s-maxage=10, stale-while-revalidate=59"
};

const axiosInstance: AxiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_SERVER_URL,
    headers: axiosDefaultHeaderOption
});

const axiosFileInstance: AxiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_FILE_SERVER_URL,
    headers: axiosDefaultHeaderOption
});

axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig<unknown>) => {
        const token = getCookie(CookieName.TOKEN);
        if (token) {
            config.headers = config.headers ?? {}; // Add this line to ensure headers is not undefined
            config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
    }
);

axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
        console.log(error);
        if (error?.response?.data?.code === 401 && window.location.pathname !== '/') {
            deleteCookie("token");
            window.location.href = "/login";
        }
        return Promise.reject(error);
    }
);


axiosFileInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig<unknown>) => {
        const token = getCookie(CookieName.TOKEN);
        if (token) {
            config.headers = config.headers ?? {}; // Add this line to ensure headers is not undefined
            config.headers["Authorization"] = `Bearer ${token}`;
        }
        return config;
    }
);

axiosFileInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error) => {
        console.log(error);
        if (error?.response?.data?.code === 401 && window.location.pathname !== '/') {
            deleteCookie("token");
            window.location.href = "/";
        }
        return Promise.reject(error);
    }
);

export const getAPIHeaders = () => axiosInstance.defaults.headers.common;

export const setAPIHeaders = (inputHeaders: RawAxiosRequestHeaders) =>
    (axiosInstance.defaults.headers.common = {
        ...axiosInstance.defaults.headers.common,
        ...inputHeaders
    });

export const getRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> =>
    axiosInstance.get(endpointApiUrl, {
        params: payload,
        ...config
    });

export const getFileRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> =>
    axiosFileInstance.get(endpointApiUrl, {
        params: payload,
        ...config
    });

export const postFileRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> =>
    axiosInstance.post(endpointApiUrl, payload, config);

export const postRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> =>
    axiosInstance.post(endpointApiUrl, payload, config);

export const putRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => axiosInstance.put(endpointApiUrl, payload, config);

export const patchRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> =>
    axiosInstance.patch(endpointApiUrl, payload, config);

export const deleteRequest = (
    endpointApiUrl: string,
    payload: Record<string, unknown> = {},
    config: AxiosRequestConfig = {}
): Promise<AxiosResponse> => {
    const queryString = buildQuery(payload);
    const urlWithQuery = `${endpointApiUrl}?${queryString}`;

    return axiosInstance.delete(urlWithQuery, config);
};

function buildQuery(params: Record<string, unknown>) {
    const query = Object.keys(params)
        .map((key) => {
            const value = params[key];
            if (Array.isArray(value)) {
                return value.map((v) => `${key}=${encodeURIComponent(v)}`).join("&");
            }
            return `${key}=${encodeURIComponent(value as string | number | boolean)}`;
        })
        .join("&");
    return query;
}

const Axios = {
    getRequest,
    getFileRequest,
    postRequest,
    postFileRequest,
    putRequest,
    patchRequest,
    deleteRequest
};

export default Axios;
