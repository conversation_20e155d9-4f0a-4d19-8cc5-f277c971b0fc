"use client";

import { FaAngleDown, FaBars, FaX, FaAngleLeft } from "react-icons/fa6";

import { useState, useLayoutEffect } from "react";
import Image from "next/image";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/context/AuthContext";

const Header = ({
  dataMenu,
  dataOfMenuChild
}: {
  dataMenu: { name: string; href: string }[];
  dataOfMenuChild: {
    title: string;
    button: { name: string }[];
    items: { name: string; href: string; image: string }[];
  }[];
}) => {
  const [isActive, setIsActive] = useState<number | null>(null);
  const [isShow, setIsShow] = useState(false);
  const pathname = usePathname();
  const [checkCondition, setCheckCondition] = useState("/logo.png");
  const { isAuthenticated, logout } = useAuth();

  useLayoutEffect(() => {
    const pathNameSplit = pathname?.split("/")[1];
    if (pathNameSplit === "kinder-garten") {
      setCheckCondition("/logo-2.svg");
    } else if (pathNameSplit === "elementary") {
      setCheckCondition("/elementary/logo.png");
    } else if (pathNameSplit === "middle-school") {
      setCheckCondition("/middle-school/logo.png");
    } else if (pathNameSplit === "high-school") {
      setCheckCondition("/high-school/logo.png");
    } else {
      setCheckCondition("/logo.png");
    }
  }, []);

  const handleSetActive = (index: number | null) => {
    if (index === null) {
      setIsActive(null);
      return;
    }
    if (dataOfMenuChild[index]?.items?.length) {
      setIsActive(isActive === index ? null : index);
    }
  };
  const handleRedirect = () => {
    if (pathname.startsWith("/kinder-garten"))
      window.location.href = "/kinder-garten";
    else if (pathname.startsWith("/elementary"))
      window.location.href = "/elementary";
    else if (pathname.startsWith("/middle-school"))
      window.location.href = "/middle-school";
    else if (pathname.startsWith("/high-school"))
      window.location.href = "/high-school";
    else window.location.href = "/";
  };
  const handleShowActive = () => {
    setIsShow(!isShow);
    setIsActive(null);
    if (!isShow) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  };
  if (pathname.startsWith("/login") || pathname.startsWith("/register")) {
    return null;
  }
  return (
    <section
      className={`group !bg-[#3438a4] ${typeof isActive === "number" && "!bg-[#3438a4]"} absolute left-0 right-0 top-0 z-[200] h-[115.19px] px-4 transition-all hover:bg-[#3438a4] lg:!px-0`}
    >
      <div className="container relative mx-auto flex h-full items-center !px-0 ">
        <div className="flex w-full items-center justify-between">
          <div
            className="w-[80px] cursor-pointer lg:w-[120px]"
            onClick={() => handleRedirect()}
          >
            <Image src={checkCondition} alt="logo" width={120} height={120} />
          </div>
          <div
            onClick={handleShowActive}
            className="flex aspect-square w-[40px] items-center justify-center rounded-full border border-[#115a9e] bg-[#115a9e] text-white"
          >
            {isShow ? <FaX /> : <FaBars />}
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-[1px] bg-white opacity-0 transition-all group-hover:h-0 lg:opacity-100"></div>
      </div>
      {/* {typeof isActive === "number" && (
        <div className="fixed bottom-0 left-0 right-0  top-0 z-50 animate-fadeIn bg-[#3438a4] px-4 pb-12">
          <div className="container relative mx-auto flex flex-col !px-0 pt-[1rem] text-white">
            <div className="mb-4 flex items-center justify-between">
              <div onClick={() => handleSetActive(null)} className="text-xl">
                <FaAngleLeft />
              </div>
              <div className="text-xl">{dataMenu[isActive]?.name}</div>
              <div
                onClick={handleShowActive}
                className="flex aspect-square w-[40px] items-center justify-center rounded-full border border-[#115a9e] bg-[#115a9e] text-white"
              >
                {isShow ? <FaX /> : <FaBars />}
              </div>
            </div>
            <div className="w-full space-y-4">
              <p className="pr-3 text-2xl font-semibold">
                {dataOfMenuChild[isActive]?.title}
              </p>
              <div className="flex w-fit flex-col gap-4">
                {dataOfMenuChild[isActive]?.button?.map((btn, btnIndex) => (
                  <button
                    key={btnIndex}
                    className="w-fit rounded-3xl bg-[#0e4280] px-4 py-2"
                  >
                    {btn?.name}
                  </button>
                ))}
              </div>
            </div>
            <div className="w-full">
              <div className="grid grid-cols-4 gap-[20px] ">
                {dataOfMenuChild[isActive]?.items?.map((item, itemIndex) => (
                  <div
                    key={itemIndex + item.href}
                    className="relative col-span-2 flex transform items-center justify-center overflow-hidden rounded-lg transition-transform duration-300 ease-in-out hover:scale-110"
                  >
                    <Link onClick={handleShowActive} href={item.href}>
                      <img
                        src={item.image}
                        alt={item.name}
                        className="h-[123px] w-[289.5px] rounded-lg object-cover sm:h-[196px] sm:w-[340.5px] md:h-[238px] md:w-[460px]"
                      />
                      <span className=" absolute inset-0 flex items-center justify-center rounded-lg bg-black bg-opacity-50 px-[1rem] text-center text-lg font-semibold text-white">
                        {item.name}
                      </span>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )} */}
      <div
        className={`fixed left-0 top-0 z-50 h-full w-full bg-black bg-opacity-50 transition-all duration-300 ${isShow ? "visible opacity-100" : "invisible opacity-0"}`}
      >
        <div
          className={`fixed right-0 top-0 z-50 h-full w-[280px] sm:w-[300px] overflow-y-auto bg-white transition-all duration-300 ${isShow ? "translate-x-0" : "translate-x-full"}`}
        >
          <div className="flex h-14 sm:h-16 items-center justify-between border-b px-3 sm:px-4">
            <div
              className="w-[70px] sm:w-[80px] lg:w-[120px] cursor-pointer"
              onClick={() => handleRedirect()}
            >
              <Image src={checkCondition} alt="logo" width={40} height={40} className="sm:w-[45px] sm:h-[45px]" />
            </div>
            <button
              onClick={handleShowActive}
              className="flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full hover:bg-gray-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <ul className="space-y-1 p-3 sm:p-4">
            {dataMenu?.map((item, index) => {
              if (item.href === "/login" && isAuthenticated) {
                return (
                  <li
                    key={index + item.href}
                    className="group flex cursor-pointer items-center gap-2"
                  >
                    <span
                      onClick={() => {
                        logout();
                        handleShowActive();
                      }}
                      className="w-full cursor-pointer rounded-md px-2.5 sm:px-3 py-1.5 sm:py-2 text-[13px] sm:text-[14px] text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    >
                      Đăng xuất
                    </span>
                  </li>
                );
              }

              const hasDropdown = !!dataOfMenuChild[index]?.items?.length;
              const isDropdownOpen = isActive === index;

              return (
                <>
                  <li
                    key={index + item.href}
                    className="group flex cursor-pointer items-center gap-2"
                  >
                    {item.href && !hasDropdown ? (
                      <Link
                        onClick={handleShowActive}
                        href={item.href}
                        className="block w-full rounded-md px-2.5 sm:px-3 py-1.5 sm:py-2 text-[13px] sm:text-[14px] text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                      >
                        {item.name}
                      </Link>
                    ) : (
                      <span
                        onClick={() => handleSetActive(index)}
                        className="block w-full cursor-pointer rounded-md px-2.5 sm:px-3 py-1.5 sm:py-2 text-[13px] sm:text-[14px] text-gray-700 hover:bg-gray-100 hover:text-gray-900 flex items-center justify-between"
                      >
                        {item.name}
                        {hasDropdown && (
                          <FaAngleDown
                            className={`${isDropdownOpen ? "rotate-180" : ""} text-gray-600 w-4 h-4 sm:w-5 sm:h-5 ml-2`}
                          />
                        )}
                      </span>
                    )}
                  </li>
                  {/* Dropdown */}
                  {hasDropdown && isDropdownOpen && (
                    <ul className="pl-4 py-1 space-y-1">
                      {dataOfMenuChild[index]?.items?.map((child, childIdx) => (
                        <li key={childIdx + child.href}>
                          <Link
                            onClick={handleShowActive}
                            href={child.href}
                            className="block rounded-md px-2 py-1 text-[13px] sm:text-[14px] text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                          >
                            {child.name}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              );
            })}
          </ul>
        </div>
      </div>
    </section>
  );
};
export default Header;
