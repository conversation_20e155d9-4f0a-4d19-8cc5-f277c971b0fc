"use client";

import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import usePostStore from "@/store/postStore";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { type ExtendedPost } from "../../app/(homepage)/social-network/profile/components/PostsTab";
import EditPostForm from "./EditPostForm";
import PostItem from "./PostItem";

interface PostContentProps {
  post: ExtendedPost | null;
  resetList: () => void;
  onEditStateChange?: (isEditing: boolean, postId: number) => void;
     handleOpenProfile: (email:string) =>void;
  hasBorder?: boolean;
  isItemView?: boolean;
  errorMessage?: string;
}

const PostContent = ({ post, resetList, onEditStateChange, handleOpenProfile,hasBorder = true, isItemView = false, errorMessage }: PostContentProps) => {
  const [isEdit, setIsEdit] = useState(false);
  
  // Thông báo cho component cha khi trạng thái chỉnh sửa thay đổi
  useEffect(() => {
    if (onEditStateChange && post) {
      onEditStateChange(isEdit, post.postSocialNetworkId);
    }
  }, [isEdit, post?.postSocialNetworkId]);

  // Xử lý chỉnh sửa bài đăng
  const handleEdit = (postId: number) => {
    setIsEdit(true);
  };

  // Xử lý hủy chỉnh sửa
  const handleCancelEdit = () => {
    setIsEdit(false);
  };

  // Lấy hàm triggerReset từ store
  const { triggerReset } = usePostStore();
  
  // Xử lý xóa bài đăng
  const handleDelete = async (postId: number) => {
    if (window.confirm("Bạn có chắc chắn muốn xóa bài đăng này không?")) {
      try {
        await socialNetworkServices.deletePostSocialNetwork(postId);
        
        if (isItemView) {
          // và đóng modal hiện tại vì bài đăng đã bị xóa
          triggerReset();
          
          // Đóng modal nếu có
          const closeModalButton = document.querySelector('.modal-close-button') as HTMLElement;
          if (closeModalButton) {
            closeModalButton.click();
          }
        } else {
          // Nếu đang trong PostsTab, gọi cả resetList và triggerReset
          resetList();
          triggerReset();
        }
        
        toast.success("Đã xóa bài đăng thành công");
      } catch (error) {
        console.error("Lỗi khi xóa bài đăng:", error);
        toast.error("Không thể xóa bài đăng");
      }
    }
  };

  // Hiển thị thông báo lỗi nếu không có bài viết
  if (!post) {
    return (
      <div className={`rounded-lg ${hasBorder ? 'border border-gray-200' : ''} bg-white p-4`}>
        <div className="flex flex-col items-center justify-center py-8">
          <svg className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">{errorMessage || "Bài viết không khả dụng"}</h3>
          <p className="mt-1 text-sm text-gray-500">Bài viết này có thể đã bị xóa hoặc không tồn tại.</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {isEdit ? (
        <EditPostForm
          post={post}
          onCancelEdit={handleCancelEdit}
          resetList={resetList}
          hasBorder={hasBorder}
        />
      ) : (
        <PostItem
          post={post}
          onEdit={handleEdit}
          onDelete={handleDelete}
          resetList={resetList}
          handleOpenProfile={handleOpenProfile}
          hasBorder={hasBorder}
        />
      )}
    </>
  );
};

export default PostContent;
