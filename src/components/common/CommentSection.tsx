"use client";

import { Post } from "@/services/social-network/types/types";
import { useState } from "react";

import CommentForm from "./CommentForm";
import CommentList from "./CommentList";

type ExtendedPost = Post & {
  avatarUrl?: string;
  fileUrls?: string[];
};

interface CommentSectionProps {
  post: ExtendedPost;
  resetList: () => void;
  handleOpenProfile: (email:string) =>void;
}

const CommentSection = ({ post, resetList ,handleOpenProfile }: CommentSectionProps) => {
  const [refreshComments, setRefreshComments] = useState(false);

  // Xử lý khi có comment mới được thêm
  const handleCommentAdded = () => {
    setRefreshComments(!refreshComments);
    resetList();
  };

  return (
    <div className="border-t border-gray-100 p-4">
      {/* Form thêm bình luận */}
      <CommentForm 
        post={post} 
        onCommentAdded={handleCommentAdded} 
      />

      {/* <PERSON>h sách bình luận */}
      <CommentList 
        postId={post.postSocialNetworkId} 
        key={refreshComments ? "refresh" : "initial"}
        handleOpenProfile={handleOpenProfile}
      />
    </div>
  );
};

export default CommentSection;
