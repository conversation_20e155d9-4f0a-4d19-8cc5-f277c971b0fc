"use client";

import { useEffect, useState } from "react";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaSpinner } from "react-icons/fa";

import usePostStore from "@/store/postStore";

import useUploadFile from "@/hook/useUploadFile";
import { cn } from "@/lib/utils";
import socialNetworkServices from "@/services/social-network/socialNetworkServices";
import { UserHistorySocialNetwork } from "@/services/social-network/types/types";
import { useRouter } from "next/navigation";
import { ExtendedPost } from "../app/(homepage)/social-network/profile/components/PostsTab";
import PostContent from "./common/PostContent";

// Dữ liệu mẫu cho người dùng đề xuất
const suggestedFriends = [
  {
    id: 1,
    name: "Nguyễn Văn <PERSON>",
    role: "Giáo viên",
    avatar: "/images/avatar-placeholder.jpg",
    mutualFriends: 5
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    role: "<PERSON><PERSON><PERSON> sin<PERSON>",
    avatar: "/images/avatar-placeholder.jpg",
    mutualFriends: 3
  },
  {
    id: 3,
    name: "<PERSON><PERSON>ăn <PERSON>",
    role: "Phụ huynh",
    avatar: "/images/avatar-placeholder.jpg",
    mutualFriends: 2
  }
];

// Hàm lấy nhãn cho loại hoạt động
const getActivityTypeLabel = (type: string): string => {
  const types: Record<string, string> = {
    CREATE: "Tạo mới",
    UNLIKE: "Bỏ thích",
    LIKE: "Đã thích",
    SAVE: "Đã lưu",
    COMMENT: "Bình luận",
    SHARE: "Chia sẻ"
  };
  return types[type] || type;
};

// Hàm lấy màu cho loại hoạt động
const getActivityTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    CREATE: "bg-green-100 text-green-600",
    UNLIKE: "bg-red-100 text-red-600",
    LIKE: "bg-pink-100 text-pink-600",
    SAVE: "bg-indigo-100 text-indigo-600",
    COMMENT: "bg-blue-100 text-blue-600",
    SHARE: "bg-yellow-100 text-yellow-600"
  };
  return colors[type] || "bg-gray-100 text-gray-600";
};

export default function History({className}: {className?: string}) {
  const router = useRouter();
  const [userHistory, setUserHistory] = useState<UserHistorySocialNetwork[]>(
    []
  );
  const [loadingUserHistory, setLoadingUserHistory] = useState(true);
  const [errorUserHistory, setErrorUserHistory] = useState<string | null>(null);
  const [currentPageUserHistory, setCurrentPageUserHistory] = useState(0);
  const [totalPagesUserHistory, setTotalPagesUserHistory] = useState(0);
  const [showPostModal, setShowPostModal] = useState(false);
  const [selectedPost, setSelectedPost] = useState<ExtendedPost | null>(null);
  const [loadingPost, setLoadingPost] = useState(false);
  const [postError, setPostError] = useState<string | null>(null);
  const { viewFile } = useUploadFile();

  // Lấy dữ liệu lịch sử hoạt động từ API
  const fetchUserHistory = async (page: number = 0) => {
    try {
      setLoadingUserHistory(true);
      const response = await socialNetworkServices.userHistorySocialNetwork({
        page,
        size: 5,
        sort: "DESC"
      });

      if (response?.data?.data) {
        setUserHistory(response.data.data.content);
        setTotalPagesUserHistory(response.data.data.totalPagesUserHistory);
        setCurrentPageUserHistory(response.data.data.number);
      }
    } catch (err) {
      console.error("Lỗi khi lấy dữ liệu lịch sử:", err);
      setErrorUserHistory("Không thể tải lịch sử hoạt động");
    } finally {
      setLoadingUserHistory(false);
    }
  };

  // Xử lý chuyển trang
  const handlePageChange = (newPage: number) => {
    if (newPage >= 0 && newPage < totalPagesUserHistory) {
      fetchUserHistory(newPage);
    }
  };

  // Xử lý điều hướng đến bài đăng
  const handleNavigateToPost = async (postId: number) => {
    try {
      setLoadingPost(true);
      setPostError(null); // Reset lỗi trước khi tải bài đăng mới
      // Vô hiệu hóa scroll cho body khi mở modal
      document.body.style.overflow = "hidden";
      const response =
        await socialNetworkServices.getPostSocialNetworkById(postId);

      if (response && response.data.data) {
        const postData = response.data.data;
        let avatarUrl = undefined;
        let fileUrls: string[] = [];
        let postReferWithUrls = null;

        // Kiểm tra nếu bài viết được tham chiếu không khả dụng
        if (postData.postRefer && !postData.referPostAvailable) {
          // Vẫn hiển thị bài đăng chính nhưng có thông báo về bài tham chiếu
          setPostError("Bài viết được tham chiếu không khả dụng");
        }

        // Nếu có thumbnail, gọi API viewFile để lấy URL
        if (postData.thumbnail) {
          try {
            avatarUrl = (await viewFile(
              postData.thumbnail,
              "social-network"
            )) as string;
          } catch (error) {
            console.error("Lỗi khi lấy URL cho thumbnail:", error);
          }
        }

        // Nếu có files, gọi API viewFile để lấy URL cho từng file
        if (postData.files && postData.files.length > 0) {
          try {
            // Gọi API viewFile cho từng fileId trong mảng files
            const filePromises = postData.files.map((fileId: string) =>
              viewFile(fileId, "social-network")
            );
            // Ép kiểu kết quả trả về thành string[]
            fileUrls = (await Promise.all(filePromises)) as string[];
          } catch (error) {
            console.error("Lỗi khi lấy URL cho files:", error);
          }
        }

        // Xử lý postRefer nếu có và khả dụng
        if (postData.postRefer && postData.referPostAvailable) {
          try {
            // Xử lý avatar của postRefer
            let referAvatarUrl = undefined;
            if (postData.postRefer.thumbnail) {
              referAvatarUrl = (await viewFile(
                postData.postRefer.thumbnail,
                "social-network"
              )) as string;
            }

            // Xử lý files của postRefer
            let referFileUrls: string[] = [];
            if (
              postData.postRefer.files &&
              postData.postRefer.files.length > 0
            ) {
              const referFilePromises = postData.postRefer.files.map(
                (fileId: string) => viewFile(fileId, "social-network")
              );
              referFileUrls = (await Promise.all(
                referFilePromises
              )) as string[];
            }

            postReferWithUrls = {
              ...postData.postRefer,
              avatarUrl: referAvatarUrl,
              fileUrls: referFileUrls
            };
          } catch (error) {
            console.error("Lỗi khi xử lý postRefer:", error);
            setPostError("Không thể tải thông tin bài viết được tham chiếu");
          }
        }

        const extendedPost: ExtendedPost = {
          ...postData,
          avatarUrl,
          fileUrls,
          postRefer: postReferWithUrls
        };

        setSelectedPost(extendedPost);
        setShowPostModal(true);
      } else {
        // Trường hợp không có dữ liệu bài đăng
        setPostError("Bài viết không khả dụng hoặc đã bị xóa");
        setShowPostModal(true); // Vẫn hiển thị modal nhưng với thông báo lỗi
      }
    } catch (error) {
      console.error("Lỗi khi lấy dữ liệu bài đăng:", error);
      setPostError("Bài viết không khả dụng hoặc đã bị xóa");
      setShowPostModal(true); // Vẫn hiển thị modal nhưng với thông báo lỗi
    } finally {
      setLoadingPost(false);
    }
  };

  // Gọi API khi component được mount
  useEffect(() => {
    fetchUserHistory();
  }, []);
  // Hàm để đóng modal và reset state
  const handleClosePostModal = () => {
    setShowPostModal(false);
    setSelectedPost(null);
    setPostError(null);
    // Khôi phục scroll cho body
    document.body.style.overflow = "auto";
  };

  // Lấy resetCounter và triggerReset từ store
  const { triggerReset } = usePostStore();

  // Hàm reset danh sách (có thể cần khi có thay đổi trên bài đăng)
  const resetPostsList = () => {
    // Cập nhật lại dữ liệu bài đăng hiện tại
    handleNavigateToPost(selectedPost?.postSocialNetworkId || 0);
    // Kích hoạt reset global để các component khác cũng được cập nhật
    triggerReset();
    fetchUserHistory(currentPageUserHistory);
  };

  return (
    <div className={cn("overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg", className)}>
      <div className="sticky top-0 border-b border-gray-200 bg-white p-3">
        <h2 className="flex items-center gap-2 text-lg font-semibold">
          <FaChartLine className="text-blue-600" />
          Hoạt động gần đây
        </h2>
      </div>
      {/* Hoạt động gần đây */}
      {loadingUserHistory ? (
        <div className="flex items-center justify-center py-4">
          <FaSpinner className="animate-spin text-xl text-blue-600" />
        </div>
      ) : errorUserHistory ? (
        <div className="py-4 text-center text-red-500">{errorUserHistory}</div>
      ) : userHistory.length === 0 ? (
        <div className="py-4 text-center text-gray-500">
          Chưa có hoạt động nào
        </div>
      ) : (
        <>
          <div>
            {userHistory.map((activity, index) => (
              <div
                key={`${activity.objectId}-${index}`}
                className="cursor-pointer border-b border-gray-100 p-4 transition-colors last:border-0 hover:bg-gray-50"
                onClick={() => handleNavigateToPost(activity.objectId)}
              >
                <div className="flex items-start gap-2">
                  <div className="mt-2 h-2 w-2 rounded-full bg-blue-500"></div>
                  <div className="flex-1">
                    <div className="space-x-2 text-sm">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${getActivityTypeColor(activity.objectType)}`}
                      >
                        {getActivityTypeLabel(activity.objectType)}
                      </span>
                      <span>{activity.content}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Phân trang */}
          {totalPagesUserHistory > 1 && (
            <div className="mt-4 flex items-center justify-center gap-2">
              <button
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentPageUserHistory === 0 ? "cursor-not-allowed bg-gray-100 text-gray-400" : "bg-blue-100 text-blue-600 hover:bg-blue-200"}`}
                onClick={() => handlePageChange(currentPageUserHistory - 1)}
                disabled={currentPageUserHistory === 0}
              >
                &lt;
              </button>

              <span className="text-sm text-gray-600">
                {currentPageUserHistory + 1}/{totalPagesUserHistory}
              </span>

              <button
                className={`flex h-8 w-8 items-center justify-center rounded-full ${currentPageUserHistory === totalPagesUserHistory - 1 ? "cursor-not-allowed bg-gray-100 text-gray-400" : "bg-blue-100 text-blue-600 hover:bg-blue-200"}`}
                onClick={() => handlePageChange(currentPageUserHistory + 1)}
                disabled={currentPageUserHistory === totalPagesUserHistory - 1}
              >
                &gt;
              </button>
            </div>
          )}
        </>
      )}

      {/* Modal hiển thị chi tiết bài đăng */}
      {showPostModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-black bg-opacity-50 md:p-4">
          <div className="relative max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-white p-4 shadow-xl">
            {/* Nút đóng */}
            <button
              className="modal-close-button absolute right-4 top-4 text-gray-500 hover:text-gray-700"
              onClick={handleClosePostModal}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            {/* Sử dụng PostContent đã được cập nhật để xử lý cả trường hợp bài viết không khả dụng */}
            <div className="mt-2">
              <PostContent
                post={selectedPost}
                resetList={resetPostsList}
                hasBorder={false}
                isItemView={true}
                errorMessage={postError || undefined}
              />
            </div>
          </div>
        </div>
      )}

      {/* Hiển thị loading khi đang tải bài đăng */}
      {loadingPost && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="rounded-lg bg-white p-6 shadow-xl">
            <div className="flex items-center space-x-3">
              <FaSpinner className="animate-spin text-2xl text-blue-600" />
              <span>Đang tải bài đăng...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
