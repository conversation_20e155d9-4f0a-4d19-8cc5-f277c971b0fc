"use client";

import { useState } from "react";
import {
  FaBell,
  FaComment,
  FaEllipsisH,
  FaHeart,
  FaShare,
  FaUserPlus
} from "react-icons/fa";
import { MdVerified } from "react-icons/md";
import DefaultAvatar from "./ui/DefaultAvatar";

// Mock data cho notifications
const mockNotifications = [
  {
    id: 1,
    type: "like",
    user: {
      name: "Nguyễn <PERSON>ăn <PERSON>",
      avatar: null,
      isVerified: true
    },
    message: "đã thích bài viết của bạn",
    postContent: "Hôm nay là một ngày tuyệt vời để học tập...",
    time: "2 phút trước"
  },
  {
    id: 2,
    type: "comment",
    user: {
      name: "Trần Th<PERSON>",
      avatar: null,
      isVerified: false
    },
    message: "đã bình luận về bài viết của bạn",
    postContent: "<PERSON>a sẻ kinh nghiệm học tập hiệu quả",
    comment: "<PERSON>ài viết rất hay và bổ ích!",
    time: "15 phút trước"
  },
  {
    id: 3,
    type: "friend_request",
    user: {
      name: "Lê Minh Cường",
      avatar: null,
      isVerified: false
    },
    message: "đã gửi lời mời kết bạn",
    time: "1 giờ trước"
  },
  {
    id: 4,
    type: "share",
    user: {
      name: "Phạm Thu Hà",
      avatar: null,
      isVerified: true
    },
    message: "đã chia sẻ bài viết của bạn",
    postContent: "Những phương pháp học sáng tạo cho trẻ em",
    time: "3 giờ trước"
  },
  {
    id: 5,
    type: "system",
    message: "Bạn có 5 bài viết mới từ những người bạn theo dõi",
    time: "1 ngày trước"
  }
];

const NotificationDropdown = () => {
  const [notifications] = useState(mockNotifications);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "like":
        return <FaHeart className="text-red-500" />;
      case "comment":
        return <FaComment className="text-blue-500" />;
      case "share":
        return <FaShare className="text-green-500" />;
      case "friend_request":
        return <FaUserPlus className="text-purple-500" />;
      case "system":
        return <FaBell className="text-gray-500" />;
      default:
        return <FaBell className="text-gray-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 w-[360px] md:w-[450px]">
      {/* Header */}
      <div className="p-3 md:p-4 border-b border-gray-200">
        <h3 className="text-base md:text-lg font-semibold text-gray-900">Thông báo</h3>
      </div>

      {/* Notifications List */}
      <div className="max-h-[60vh] md:max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-8 text-center">
            <FaBell className="mx-auto text-4xl text-gray-300 mb-3" />
            <p className="text-gray-500">Chưa có thông báo nào</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="p-3 md:p-4 hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div className="flex items-start space-x-2 md:space-x-3">
                  {/* Avatar or Icon */}
                  <div className="flex-shrink-0">
                    {notification.user ? (
                      <div className="relative">
                        <DefaultAvatar
                          name={notification.user.name}
                          size={36}
                          className="w-8 h-8 md:w-10 md:h-10"
                        />
                        <div className="absolute -bottom-0.5 -right-0.5 md:-bottom-1 md:-right-1 bg-white rounded-full p-0.5 md:p-1">
                          <div className="text-xs md:text-sm">
                            {getNotificationIcon(notification.type)}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="w-8 h-8 md:w-10 md:h-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <div className="text-xs md:text-sm">
                          {getNotificationIcon(notification.type)}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-1 mb-1">
                      {notification.user && (
                        <>
                          <span className="font-medium text-gray-900 text-sm md:text-base truncate">
                            {notification.user.name}
                          </span>
                          {notification.user.isVerified && (
                            <MdVerified className="text-blue-500 text-xs md:text-sm flex-shrink-0" />
                          )}
                        </>
                      )}
                      <span className="text-gray-600 text-xs md:text-sm">
                        {notification.message}
                      </span>
                    </div>

                    {/* Post content preview */}
                    {notification.postContent && (
                      <p className="text-xs md:text-sm text-gray-500 mb-1 line-clamp-2">
                        "{notification.postContent}"
                      </p>
                    )}

                    {/* Comment preview */}
                    {notification.comment && (
                      <p className="text-xs md:text-sm text-blue-600 mb-1 italic line-clamp-1">
                        "{notification.comment}"
                      </p>
                    )}

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-400">
                        {notification.time}
                      </span>
                    </div>
                  </div>

                  {/* Action menu */}
                  <div className="flex-shrink-0">
                    <button className="text-gray-400 hover:text-gray-600 p-1">
                      <FaEllipsisH className="text-sm" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-2 md:p-3 border-t border-gray-200 bg-gray-50">
        <button className="w-full text-center text-xs md:text-sm text-blue-600 hover:text-blue-800 font-medium py-1.5 md:py-2">
          Xem tất cả thông báo
        </button>
      </div>
    </div>
  );
};

export default NotificationDropdown;
